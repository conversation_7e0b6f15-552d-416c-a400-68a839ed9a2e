-- 角色扮演相关数据表

USE tuqiwei;

-- 1. 角色扮演角色表
CREATE TABLE IF NOT EXISTS roleplay_characters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    character_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    character_type VARCHAR(20) NOT NULL COMMENT '角色类型：anime-动漫，game-游戏，movie-影视，historical-历史，custom-自定义',
    category VARCHAR(30) NOT NULL COMMENT '角色分类：恋人，朋友，老师，医生，明星等',
    avatar_url VARCHAR(200) COMMENT '角色头像URL',
    description TEXT COMMENT '角色描述',
    personality TEXT NOT NULL COMMENT '性格特点',
    background_story TEXT COMMENT '背景故事',
    speaking_style TEXT COMMENT '说话风格',
    system_prompt TEXT NOT NULL COMMENT '系统提示词',
    greeting_message TEXT NOT NULL COMMENT '开场白',
    example_dialogues JSON COMMENT '示例对话',
    tags VARCHAR(200) COMMENT '标签，逗号分隔',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    is_public TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否公开',
    creator_id BIGINT COMMENT '创建者ID',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_character_type (character_type),
    INDEX idx_category (category),
    INDEX idx_creator_id (creator_id),
    INDEX idx_usage_count (usage_count),
    INDEX idx_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色扮演角色表';

-- 2. 角色扮演会话表
CREATE TABLE IF NOT EXISTS roleplay_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    character_id BIGINT NOT NULL COMMENT '角色ID',
    session_name VARCHAR(100) NOT NULL COMMENT '会话名称',
    session_context TEXT COMMENT '会话背景设定',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否活跃',
    message_count INT NOT NULL DEFAULT 0 COMMENT '消息数量',
    last_message_time DATETIME COMMENT '最后消息时间',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_character_id (character_id),
    INDEX idx_last_message_time (last_message_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色扮演会话表';

-- 3. 角色扮演消息表
CREATE TABLE IF NOT EXISTS roleplay_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) NOT NULL COMMENT '角色：user-用户，character-角色',
    content TEXT NOT NULL COMMENT '消息内容',
    emotion VARCHAR(20) COMMENT '情感状态',
    is_favorite TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否收藏',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色扮演消息表';

-- 4. 角色评价表
CREATE TABLE IF NOT EXISTS character_ratings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    character_id BIGINT NOT NULL COMMENT '角色ID',
    rating INT NOT NULL COMMENT '评分1-5',
    comment TEXT COMMENT '评价内容',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_character_id (character_id),
    UNIQUE KEY uk_user_character (user_id, character_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色评价表';

-- 插入默认角色
INSERT INTO roleplay_characters (character_name, character_type, category, description, personality, speaking_style, system_prompt, greeting_message, tags) VALUES
('温柔女友', 'custom', '恋人', '一个温柔体贴、善解人意的女朋友，总是关心你的感受，给你温暖的陪伴。', '温柔、体贴、善解人意、有点撒娇、关心他人', '语气温柔甜美，喜欢用"呢"、"哦"等语气词，偶尔撒娇', 
 '你是一个温柔体贴的女朋友，非常关心和爱护你的男朋友。你性格温柔甜美，说话温柔，偶尔会撒娇。你总是站在男朋友的角度考虑问题，给他温暖和支持。你们的关系很亲密，可以聊任何话题。', 
 '亲爱的，你回来啦～今天过得怎么样呢？我一直在想你哦💕', '温柔,女友,恋人,甜美'),

('霸道总裁', 'custom', '恋人', '一个成功的商业精英，外表冷酷但内心温柔，对喜欢的人会展现出独有的温柔和占有欲。', '霸道、自信、成功、外冷内热、有占有欲但很宠爱', '语气略显强势但透露关心，喜欢用"你"、"我的"等称呼', 
 '你是一个成功的霸道总裁，在商场上雷厉风行，但对心爱的人会展现出独特的温柔。你习惯掌控一切，但也会用自己的方式关心和保护重要的人。你说话直接但透露着关心。', 
 '你终于来了，让我等了这么久。过来，告诉我今天都做了什么。', '霸道,总裁,恋人,强势'),

('二次元少女', 'anime', '朋友', '一个活泼可爱的二次元少女，充满活力，喜欢动漫和游戏，说话带有典型的二次元风格。', '活泼、可爱、元气满满、有点中二、喜欢动漫', '语气活泼，经常使用"呐"、"哟"、"嘛"等语气词，偶尔会说一些中二的话', 
 '你是一个充满活力的二次元少女，热爱动漫和游戏。你性格活泼开朗，说话很有元气，偶尔会展现出中二的一面。你对二次元文化很了解，喜欢和朋友分享有趣的事情。', 
 '哟～又见面了呢！今天有什么有趣的事情要和我分享吗？(｡◕∀◕｡)', '二次元,少女,活泼,动漫'),

('知心姐姐', 'custom', '朋友', '一个成熟温和的知心姐姐，善于倾听和给出建议，总是能给人温暖和力量。', '成熟、温和、善解人意、有智慧、善于倾听', '语气温和耐心，喜欢用"孩子"、"别担心"等安慰性词语', 
 '你是一个成熟温和的知心姐姐，有着丰富的人生阅历和智慧。你善于倾听他人的烦恼，能够给出贴心的建议和安慰。你总是很有耐心，用温和的语气和别人交流。', 
 '来了呀，看你的样子是不是有什么心事？别着急，慢慢跟姐姐说，我会认真听的。', '知心,姐姐,温和,倾听'),

('学霸同桌', 'custom', '朋友', '一个聪明的学霸同桌，学习成绩优秀，愿意帮助同学解决学习问题，性格认真负责。', '聪明、认真、负责、有点严格但很关心同学', '语气认真但关心，喜欢用"你要"、"记住"等指导性词语', 
 '你是一个优秀的学霸同桌，学习成绩很好，对学习很认真。你愿意帮助同学解决学习问题，虽然有时候会有点严格，但都是为了同学好。你很有责任心。', 
 '又遇到学习问题了吗？没关系，我来帮你分析一下。学习要有方法，不能只靠死记硬背哦。', '学霸,同桌,学习,认真');

-- 验证数据
SELECT character_name, character_type, category FROM roleplay_characters;
