-- 添加头像相关字段

-- 1. 在用户表中添加头像字段
ALTER TABLE users ADD COLUMN avatar_url VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL' AFTER email;

-- 2. 创建AI头像配置表（先不加外键约束）
CREATE TABLE IF NOT EXISTS ai_avatars (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    avatar_name VARCHAR(50) NOT NULL COMMENT '头像名称',
    avatar_url VARCHAR(255) NOT NULL COMMENT '头像URL',
    is_default TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为默认头像',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI头像配置表';

-- 3. 插入默认AI头像
INSERT INTO ai_avatars (user_id, avatar_name, avatar_url, is_default) VALUES
(1, '默认AI助手', '/images/avatars/ai-default.png', 1),
(1, '机器人', '/images/avatars/ai-robot.png', 0),
(1, '智能助手', '/images/avatars/ai-smart.png', 0);
