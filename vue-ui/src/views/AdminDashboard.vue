<template>
  <div class="admin-dashboard">
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon user-icon">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ userStats.totalUsers || 0 }}</div>
          <div class="stat-label">总用户数</div>
          <div class="stat-extra">
            今日新增: {{ userStats.todayNewUsers || 0 }}
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon active-icon">
          <el-icon><UserFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ userStats.activeUsers || 0 }}</div>
          <div class="stat-label">活跃用户</div>
          <div class="stat-extra">
            今日活跃: {{ userStats.todayActiveUsers || 0 }}
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon chat-icon">
          <el-icon><ChatDotRound /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ chatStats.totalSessions || 0 }}</div>
          <div class="stat-label">聊天会话</div>
          <div class="stat-extra">
            今日会话: {{ chatStats.todaySessions || 0 }}
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon game-icon">
          <el-icon><Trophy /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ gameStats.totalRecords || 0 }}</div>
          <div class="stat-label">游戏记录</div>
          <div class="stat-extra">
            今日游戏: {{ gameStats.todayRecords || 0 }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-card">
        <div class="chart-header">
          <h3>📈 用户趋势</h3>
          <p>最近7天用户注册和活跃情况</p>
        </div>
        <div class="chart-content">
          <div class="trend-chart">
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color register"></span>
                新注册用户
              </span>
              <span class="legend-item">
                <span class="legend-color active"></span>
                活跃用户
              </span>
            </div>
            <div class="chart-bars">
              <div
                v-for="(item, index) in trendData"
                :key="index"
                class="chart-bar-group"
              >
                <div class="chart-bars-container">
                  <div
                    class="chart-bar register-bar"
                    :style="{ height: getBarHeight(item.registerCount, maxRegisterCount) }"
                  ></div>
                  <div
                    class="chart-bar active-bar"
                    :style="{ height: getBarHeight(item.activeCount, maxActiveCount) }"
                  ></div>
                </div>
                <div class="chart-label">{{ formatDate(item.date) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-card">
        <div class="chart-header">
          <h3>🎯 功能使用统计</h3>
          <p>各功能模块使用情况</p>
        </div>
        <div class="chart-content">
          <div class="usage-stats">
            <div class="usage-item">
              <div class="usage-label">AI聊天</div>
              <div class="usage-bar">
                <div
                  class="usage-fill chat-fill"
                  :style="{ width: getUsagePercent(chatStats.totalMessages, totalUsage) }"
                ></div>
              </div>
              <div class="usage-value">{{ chatStats.totalMessages || 0 }}</div>
            </div>

            <div class="usage-item">
              <div class="usage-label">游戏娱乐</div>
              <div class="usage-bar">
                <div
                  class="usage-fill game-fill"
                  :style="{ width: getUsagePercent(gameStats.totalRecords, totalUsage) }"
                ></div>
              </div>
              <div class="usage-value">{{ gameStats.totalRecords || 0 }}</div>
            </div>

            <div class="usage-item">
              <div class="usage-label">角色扮演</div>
              <div class="usage-bar">
                <div
                  class="usage-fill roleplay-fill"
                  :style="{ width: '25%' }"
                ></div>
              </div>
              <div class="usage-value">-</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="action-card">
        <h3>🚀 快速操作</h3>
        <div class="action-buttons">
          <el-button type="primary" @click="$router.push('/admin/users')">
            <el-icon><User /></el-icon>
            用户管理
          </el-button>
          <el-button type="success" @click="$router.push('/admin/ai-roles')">
            <el-icon><Robot /></el-icon>
            AI角色
          </el-button>
          <el-button type="warning" @click="$router.push('/admin/games')">
            <el-icon><Trophy /></el-icon>
            游戏管理
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import {
  User,
  UserFilled,
  ChatDotRound,
  Trophy,
  Robot
} from '@element-plus/icons-vue'

const userStats = ref({})
const chatStats = ref({})
const gameStats = ref({})
const trendData = ref([])

// 计算总使用量
const totalUsage = computed(() => {
  return (chatStats.value.totalMessages || 0) + (gameStats.value.totalRecords || 0)
})

// 计算最大值用于图表缩放
const maxRegisterCount = computed(() => {
  return Math.max(...trendData.value.map(item => item.registerCount || 0), 1)
})

const maxActiveCount = computed(() => {
  return Math.max(...trendData.value.map(item => item.activeCount || 0), 1)
})

// 获取统计数据
const loadDashboardStats = async () => {
  try {
    const response = await axios.get('/api/admin/dashboard/stats')
    if (response.data.code === 200) {
      const data = response.data.data
      userStats.value = data.userStats || {}
      chatStats.value = data.chatStats || {}
      gameStats.value = data.gameStats || {}
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 获取趋势数据
const loadTrendData = async () => {
  try {
    const response = await axios.get('/api/admin/dashboard/trends')
    if (response.data.code === 200) {
      const data = response.data.data

      // 合并注册和活跃用户数据
      const registerMap = new Map()
      const activeMap = new Map()

      data.userTrend?.forEach(item => {
        registerMap.set(item.date, item.count)
      })

      data.activeUserTrend?.forEach(item => {
        activeMap.set(item.date, item.count)
      })

      // 生成最近7天的数据
      const result = []
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]

        result.push({
          date: dateStr,
          registerCount: registerMap.get(dateStr) || 0,
          activeCount: activeMap.get(dateStr) || 0
        })
      }

      trendData.value = result
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error)
  }
}

// 计算柱状图高度
const getBarHeight = (value, maxValue) => {
  if (maxValue === 0) return '0%'
  return Math.max((value / maxValue) * 100, 2) + '%'
}

// 计算使用率百分比
const getUsagePercent = (value, total) => {
  if (total === 0) return '0%'
  return (value / total) * 100 + '%'
}

// 格式化日期
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return (date.getMonth() + 1) + '/' + date.getDate()
}

onMounted(() => {
  loadDashboardStats()
  loadTrendData()
})
</script>

<style scoped>
.admin-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.active-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.chat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.game-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-extra {
  font-size: 12px;
  color: #909399;
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.chart-header p {
  margin: 0 0 20px 0;
  color: #909399;
  font-size: 14px;
}

/* 趋势图表 */
.chart-legend {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.register {
  background: #409eff;
}

.legend-color.active {
  background: #67c23a;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 12px;
  height: 200px;
  padding: 0 10px;
}

.chart-bar-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.chart-bars-container {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 180px;
  width: 100%;
  justify-content: center;
}

.chart-bar {
  width: 12px;
  border-radius: 2px 2px 0 0;
  min-height: 2px;
  transition: all 0.3s;
}

.register-bar {
  background: #409eff;
}

.active-bar {
  background: #67c23a;
}

.chart-label {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

/* 使用统计 */
.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.usage-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
  text-align: right;
}

.usage-bar {
  flex: 1;
  height: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.chat-fill {
  background: #4facfe;
}

.game-fill {
  background: #43e97b;
}

.roleplay-fill {
  background: #f093fb;
}

.usage-value {
  width: 60px;
  text-align: center;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.action-card h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .chart-bars {
    gap: 8px;
  }

  .chart-bar {
    width: 8px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .stat-card {
    padding: 16px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 24px;
  }

  .chart-card {
    padding: 16px;
  }

  .chart-legend {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
