<template>
  <div class="placeholder-container">
    <div class="placeholder-content">
      <div class="placeholder-icon">💻</div>
      <h2>程序</h2>
      <p>实用程序工具箱，提供各种便捷的开发和办公工具</p>
      <div class="features-preview">
        <div class="feature-item">
          <span>🔧 开发工具</span>
        </div>
        <div class="feature-item">
          <span>📁 文件处理</span>
        </div>
        <div class="feature-item">
          <span>🌐 网络工具</span>
        </div>
        <div class="feature-item">
          <span>🧮 计算工具</span>
        </div>
      </div>
      <el-button type="primary" disabled>即将上线</el-button>
    </div>
  </div>
</template>

<script setup>
// 程序工具页面
</script>

<style scoped>
.placeholder-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.placeholder-content {
  max-width: 500px;
  padding: 40px;
}

.placeholder-icon {
  font-size: 80px;
  color: #e6a23c;
  margin-bottom: 20px;
}

.placeholder-content h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 16px;
}

.placeholder-content p {
  color: #606266;
  font-size: 16px;
  margin-bottom: 30px;
  line-height: 1.6;
}

.features-preview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #606266;
}

.feature-item .el-icon {
  color: #e6a23c;
}

@media (max-width: 480px) {
  .placeholder-content {
    padding: 20px;
  }
  
  .placeholder-icon {
    font-size: 60px;
  }
  
  .placeholder-content h2 {
    font-size: 24px;
  }
  
  .features-preview {
    grid-template-columns: 1fr;
  }
}
</style>
