<template>
  <div class="games-container">
    <!-- 游戏列表界面 -->
    <div v-if="gameStage === 'list'" class="games-list">
      <div class="games-content">
        <div class="games-header">
          <h2>🎮 休闲小游戏</h2>
          <p>选择一个游戏，开始您的娱乐时光</p>
        </div>

        <!-- 游戏列表 -->
        <div class="game-grid">
          <div
            v-for="game in gameList"
            :key="game.id"
            class="game-card"
            @click="selectGame(game)"
          >
            <div class="game-icon">
              <img :src="game.gameIcon || getGameIcon(game.gameCode)" :alt="game.gameName" />
            </div>
            <div class="game-info">
              <h4>{{ game.gameName }}</h4>
              <p class="game-description">{{ game.gameDescription }}</p>
              <div class="game-stats" v-if="getUserGameStat(game.gameCode)">
                <span class="best-score">最高分: {{ getUserGameStat(game.gameCode)?.bestScore || 0 }}</span>
                <span class="total-games">游戏次数: {{ getUserGameStat(game.gameCode)?.totalGames || 0 }}</span>
              </div>
              <div class="game-actions">
                <el-button type="primary" size="small">开始游戏</el-button>
                <el-button
                  v-if="hasGameProgress(game.gameCode)"
                  type="success"
                  size="small"
                  @click.stop="continueGame(game.gameCode)"
                >
                  继续游戏
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 2048游戏界面 -->
    <div v-else-if="gameStage === '2048'" class="game-2048">
      <!-- 游戏头部 -->
      <div class="game-header">
        <div class="header-left">
          <el-button @click="backToGameList" type="text" class="back-button">
            ← 返回游戏列表
          </el-button>
          <h3>🎯 2048</h3>
        </div>
        <div class="header-right">
          <div class="game-score">
            <span class="score-label">分数</span>
            <span class="score-value">{{ currentScore }}</span>
          </div>
          <div class="best-score">
            <span class="score-label">最高分</span>
            <span class="score-value">{{ bestScore }}</span>
          </div>
        </div>
      </div>

      <!-- 游戏控制栏 -->
      <div class="game-controls">
        <div class="control-left">
          <el-select v-model="gameLevel" @change="onLevelChange" :disabled="gameStarted">
            <el-option label="简单 (4x4)" :value="1"></el-option>
            <el-option label="中等 (5x5)" :value="2"></el-option>
            <el-option label="困难 (6x6)" :value="3"></el-option>
          </el-select>

          <el-select v-model="gameTheme" @change="onThemeChange">
            <el-option label="经典主题" value="classic"></el-option>
            <el-option label="彩色主题" value="colorful"></el-option>
            <el-option label="暗黑主题" value="dark"></el-option>
          </el-select>
        </div>

        <div class="control-right">
          <el-button @click="showHint" :disabled="!gameStarted">💡 提示</el-button>
          <el-button @click="pauseGame" v-if="gameStarted && !gamePaused">⏸️ 暂停</el-button>
          <el-button @click="resumeGame" v-if="gameStarted && gamePaused">▶️ 继续</el-button>
          <el-button @click="restartGame" :disabled="!gameStarted">🔄 重新开始</el-button>
          <el-button @click="startNewGame" v-if="!gameStarted" type="primary">🎮 开始游戏</el-button>
        </div>
      </div>

      <!-- 游戏画布 -->
      <div class="game-canvas-container">
        <canvas
          ref="gameCanvas"
          :width="canvasSize"
          :height="canvasSize"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          :class="['game-canvas', gameTheme, { paused: gamePaused }]"
        ></canvas>

        <!-- 游戏暂停遮罩 -->
        <div v-if="gamePaused" class="pause-overlay">
          <div class="pause-content">
            <h3>⏸️ 游戏已暂停</h3>
            <p>点击继续按钮恢复游戏</p>
          </div>
        </div>

        <!-- 游戏结束遮罩 -->
        <div v-if="gameOver" class="game-over-overlay">
          <div class="game-over-content">
            <h3>{{ gameWon ? '🎉 恭喜获胜！' : '😔 游戏结束' }}</h3>
            <p>最终分数: {{ currentScore }}</p>
            <p v-if="isNewRecord" class="new-record">🏆 新纪录！</p>
            <div class="game-over-actions">
              <el-button @click="restartGame" type="primary">再来一局</el-button>
              <el-button @click="backToGameList">返回游戏列表</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 游戏说明 -->
      <div class="game-instructions">
        <h4>🎯 游戏说明</h4>
        <p>使用方向键或滑动手势移动方块，相同数字的方块会合并。目标是合成2048！</p>
        <div class="key-hints">
          <span class="key">↑</span>
          <span class="key">↓</span>
          <span class="key">←</span>
          <span class="key">→</span>
          <span class="hint-text">使用方向键控制</span>
        </div>
      </div>
    </div>

    <!-- 象棋游戏界面 -->
    <div v-else-if="gameStage === 'chess'" class="chess-game">
      <!-- 游戏头部 -->
      <div class="game-header">
        <div class="header-left">
          <el-button @click="backToGameList" type="text" class="back-button">
            ← 返回游戏列表
          </el-button>
          <h3>♟️ 中国象棋</h3>
        </div>
        <div class="header-right">
          <div class="game-timer">
            <span class="timer-label">游戏时长</span>
            <span class="timer-value">{{ formatGameTime() }}</span>
          </div>
          <div class="current-player">
            <span class="player-label">当前玩家</span>
            <span class="player-value" :class="chessCurrentPlayer === 1 ? 'red-player' : 'black-player'">
              {{ chessCurrentPlayer === 1 ? '红方' : '黑方' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 游戏主体 -->
      <div class="chess-main">
        <!-- 棋盘区域 -->
        <div class="chess-board-container">
          <canvas
            ref="chessCanvas"
            width="480"
            height="540"
            @click="handleChessClick"
            class="chess-canvas"
          ></canvas>

          <!-- 将军提示 -->
          <div v-if="chessInCheck" class="check-alert">
            <span>将军！</span>
          </div>

          <!-- 悔棋请求提示 -->
          <div v-if="chessUndoRequested" class="undo-request">
            <div class="undo-content">
              <p>{{ chessUndoRequestPlayer === 1 ? '红方' : '黑方' }}请求悔棋</p>
              <div class="undo-actions">
                <el-button @click="respondUndo(true)" type="primary" size="small">同意</el-button>
                <el-button @click="respondUndo(false)" size="small">拒绝</el-button>
              </div>
            </div>
          </div>

          <!-- 游戏结束遮罩 -->
          <div v-if="chessGameStatus !== 'PLAYING'" class="game-over-overlay">
            <div class="game-over-content">
              <h3>{{ getGameEndMessage() }}</h3>
              <div class="game-over-actions">
                <el-button @click="startNewChessGame" type="primary">再来一局</el-button>
                <el-button @click="backToGameList">返回游戏列表</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="chess-sidebar">
          <!-- 控制按钮 -->
          <div class="chess-controls">
            <el-button @click="requestUndo" :disabled="chessMoveHistory.length === 0 || chessUndoRequested">
              悔棋
            </el-button>
            <el-button @click="surrenderGame" type="danger">认输</el-button>
            <el-button @click="saveChessGame">保存棋局</el-button>
            <el-button @click="showLoadDialog">加载棋局</el-button>
          </div>

          <!-- 棋谱记录 -->
          <div class="chess-notation">
            <h4>棋谱记录</h4>
            <div class="notation-list">
              <div
                v-for="(move, index) in chessMoveHistory"
                :key="index"
                class="notation-item"
              >
                <span class="move-number">{{ index + 1 }}.</span>
                <span class="move-text">{{ move }}</span>
              </div>
            </div>
          </div>

          <!-- 游戏说明 -->
          <div class="chess-instructions">
            <h4>♟️ 游戏说明</h4>
            <p>点击棋子选择，再点击目标位置移动。红方先行，轮流下棋。</p>
            <ul>
              <li>将死对方获胜</li>
              <li>可以悔棋一步（需对方同意）</li>
              <li>可以主动认输</li>
              <li>支持保存和加载棋局</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 转盘游戏界面 -->
    <div v-else-if="gameStage === 'wheel'" class="wheel-game">
      <!-- 转盘类型选择界面 -->
      <div v-if="wheelStage === 'types'" class="wheel-types">
        <div class="game-header">
          <div class="header-left">
            <el-button @click="backToGameList" type="text" class="back-button">
              ← 返回游戏列表
            </el-button>
            <h3>🎯 转盘游戏</h3>
          </div>
        </div>

        <div class="wheel-types-content">
          <div class="wheel-types-header">
            <h2>🎲 选择转盘类型</h2>
            <p>选择一个转盘，让它帮你做决定吧！</p>
          </div>

          <div class="wheel-types-grid">
            <div
              v-for="wheelType in wheelTypes"
              :key="wheelType.type"
              class="wheel-type-card"
              @click="selectWheelType(wheelType.type)"
            >
              <div class="wheel-type-icon">{{ wheelType.icon }}</div>
              <h4>{{ wheelType.name }}</h4>
              <p>{{ wheelType.description }}</p>
              <el-button type="primary" size="small">开始转盘</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 转盘游戏界面 -->
      <div v-else-if="wheelStage === 'wheel'" class="wheel-spin">
        <div class="game-header">
          <div class="header-left">
            <el-button @click="backToWheelTypes" type="text" class="back-button">
              ← 返回转盘选择
            </el-button>
            <h3>{{ getWheelTypeName(selectedWheelType) }}</h3>
          </div>
          <div class="header-right">
            <el-button @click="handleEditDialogOpen" type="primary" size="small">
              ✏️ 编辑转盘
            </el-button>
            <el-button @click="handleRecordsDialogOpen" size="small">
              📋 历史记录
            </el-button>
          </div>
        </div>

        <!-- 转盘容器 -->
        <div class="wheel-container">
          <div class="wheel-wrapper">
            <div
              class="wheel-disc"
              :style="!isSpinning ? { transform: `rotate(${wheelRotation}deg)` } : {}"
              :class="{ spinning: isSpinning }"
            >
              <div
                v-for="(config, index) in wheelConfigs"
                :key="index"
                class="wheel-slice"
                :style="getSliceStyle(index)"
              >
                <div class="wheel-text">{{ config.content }}</div>
              </div>
            </div>
            <div class="wheel-pointer"></div>
          </div>

          <div class="wheel-controls">
            <el-button
              @click="spinWheel"
              type="primary"
              size="large"
              :disabled="isSpinning || wheelConfigs.length === 0"
              :loading="isSpinning"
            >
              {{ isSpinning ? '转盘中...' : '开始转盘' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 转盘结果对话框 -->
    <el-dialog
      v-model="showResultDialog"
      title="🎉 转盘结果"
      width="400px"
      :close-on-click-modal="false"
      center
    >
      <div class="result-content">
        <div class="result-icon">🎯</div>
        <h3>{{ spinResult }}</h3>
        <p>转盘类型：{{ getWheelTypeName(selectedWheelType) }}</p>
      </div>
      <template #footer>
        <div class="result-actions">
          <el-button @click="spinAgain" type="primary">再转一次</el-button>
          <el-button @click="showResultDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑转盘对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="`编辑转盘 - ${getWheelTypeName(selectedWheelType)}`"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="edit-content">
        <el-table :data="editConfigs" border style="width: 100%">
          <el-table-column prop="slotIndex" label="序号" width="80" align="center">
            <template #default="{ row }">
              {{ row.slotIndex + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="内容" min-width="200">
            <template #default="{ row }">
              <el-input v-model="row.content" placeholder="请输入转盘内容" maxlength="20" />
            </template>
          </el-table-column>
          <el-table-column label="权重" width="200">
            <template #default="{ row }">
              <el-slider
                v-model="row.weight"
                :min="1"
                :max="100"
                show-input
                :show-input-controls="false"
                input-size="small"
              />
            </template>
          </el-table-column>
        </el-table>
        <div class="edit-tips">
          <p>💡 提示：</p>
          <ul>
            <li>最多支持12个转盘选项，空内容的选项将被忽略</li>
            <li>权重越高，被转到的概率越大（1-100）</li>
            <li>保存后转盘会自动调整大小和比例</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <div class="edit-actions">
          <el-button @click="resetWheelConfig" type="warning">重置为默认</el-button>
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button @click="saveWheelConfig" type="primary">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      v-model="showRecordsDialog"
      title="📋 转盘历史记录"
      width="600px"
    >
      <div class="records-content">
        <div class="records-header">
          <el-select v-model="wheelRecordsSize" @change="loadWheelRecords" style="width: 120px">
            <el-option label="5条/页" :value="5"></el-option>
            <el-option label="10条/页" :value="10"></el-option>
          </el-select>
          <el-button @click="clearWheelRecords" type="danger" size="small">清空记录</el-button>
        </div>

        <el-table :data="wheelRecords" border style="width: 100%" max-height="400">
          <el-table-column prop="spinTime" label="时间" width="180" align="center">
            <template #default="{ row }">
              {{ formatTime(row.spinTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="wheelTypeName" label="转盘类型" width="100" align="center" />
          <el-table-column prop="resultContent" label="结果" align="center" />
        </el-table>

        <div class="records-pagination" v-if="wheelRecordsTotal > 0">
          <el-pagination
            v-model:current-page="wheelRecordsPage"
            :page-size="wheelRecordsSize"
            :total="wheelRecordsTotal"
            layout="prev, pager, next"
            @current-change="loadWheelRecords"
            small
          />
        </div>

        <div v-if="wheelRecords.length === 0" class="no-records">
          <p>暂无转盘记录</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 游戏状态管理
const gameStage = ref('list') // 'list' | '2048' | 'chess' | 'wheel'
const gameList = ref([])
const userGameStats = ref([])
const gameProgressList = ref([])
const selectedGame = ref(null)

// 2048游戏相关数据
const gameCanvas = ref()
const canvasSize = ref(400)
const gameLevel = ref(1) // 1=4x4, 2=5x5, 3=6x6
const gameTheme = ref('classic')
const gameStarted = ref(false)
const gamePaused = ref(false)
const gameOver = ref(false)
const gameWon = ref(false)
const currentScore = ref(0)
const bestScore = ref(0)
const isNewRecord = ref(false)
const gameBoard = ref([])
const gameSize = ref(4)
const startTime = ref(0)
const playTime = ref(0)

// 触摸控制相关
const touchStartX = ref(0)
const touchStartY = ref(0)

// 转盘游戏相关数据
const wheelStage = ref('types') // 'types' | 'wheel' | 'edit' | 'records'
const wheelTypes = ref([])
const selectedWheelType = ref('')
const wheelConfigs = ref([])
const wheelRecords = ref([])
const wheelRecordsTotal = ref(0)
const wheelRecordsPage = ref(1)
const wheelRecordsSize = ref(10)
const isSpinning = ref(false)
const spinResult = ref('')
const showResultDialog = ref(false)
const showEditDialog = ref(false)
const showRecordsDialog = ref(false)
const editConfigs = ref([])
const wheelRotation = ref(0)

// 象棋游戏相关数据
const chessGameId = ref('')
const chessBoardState = ref('')
const chessCurrentPlayer = ref(1)
const chessGameStatus = ref('PLAYING')
const chessMoveHistory = ref([])
const chessUndoRequested = ref(false)
const chessUndoRequestPlayer = ref(0)
const chessWinner = ref('')
const chessGameStartTime = ref(0)
const chessSelectedPiece = ref(null)
const chessPossibleMoves = ref([])
const chessInCheck = ref(false)

// 加载游戏列表
const loadGameList = async () => {
  try {
    // 初始化游戏列表
    initGameList()

    // 加载用户游戏统计
    try {
      const statsResponse = await axios.get('/api/games/stats/all')
      if (statsResponse.data.code === 200) {
        userGameStats.value = statsResponse.data.data
      }
    } catch (error) {
      console.log('游戏统计接口暂未实现')
    }
  } catch (error) {
    console.error('加载游戏列表失败:', error)
    ElMessage.error('加载游戏列表失败')
  }
}

const getGameIcon = (gameCode) => {
  const gameIcons = {
    '2048': 'https://api.dicebear.com/7.x/shapes/svg?seed=2048&backgroundColor=f59e0b',
    'chess': 'https://api.dicebear.com/7.x/shapes/svg?seed=chess&backgroundColor=dc2626'
  }
  return gameIcons[gameCode] || 'https://api.dicebear.com/7.x/shapes/svg?seed=game&backgroundColor=6366f1'
}

const getUserGameStat = (gameCode) => {
  return userGameStats.value.find(stat => stat.gameCode === gameCode)
}

const hasGameProgress = (gameCode) => {
  return gameProgressList.value.some(progress => progress.gameCode === gameCode)
}

const selectGame = (game) => {
  selectedGame.value = game
  if (game.gameCode === '2048') {
    enter2048Game()
  } else if (game.gameCode === 'chess') {
    enterChessGame()
  } else if (game.gameCode === 'wheel') {
    enterWheelGame()
  }
}

const enter2048Game = async () => {
  gameStage.value = '2048'

  // 获取用户2048统计数据
  try {
    const response = await axios.get('/api/games/2048/stats')
    if (response.data.code === 200) {
      const stats = response.data.data
      bestScore.value = stats.bestScore || 0
    }
  } catch (error) {
    console.error('获取游戏统计失败:', error)
  }

  // 检查是否有游戏进度
  try {
    const progressResponse = await axios.get('/api/games/2048/progress')
    if (progressResponse.data.code === 200 && progressResponse.data.data) {
      // 有游戏进度，询问是否继续
      ElMessageBox.confirm('检测到未完成的游戏，是否继续？', '继续游戏', {
        confirmButtonText: '继续游戏',
        cancelButtonText: '开始新游戏',
        type: 'info'
      }).then(() => {
        continueGame('2048')
      }).catch(() => {
        // 用户选择开始新游戏
      })
    }
  } catch (error) {
    console.error('检查游戏进度失败:', error)
  }

  await nextTick()
  initCanvas()
}

// backToGameList函数已在文件末尾定义

const resetGameState = () => {
  gameStarted.value = false
  gamePaused.value = false
  gameOver.value = false
  gameWon.value = false
  currentScore.value = 0
  gameBoard.value = []
  playTime.value = 0
}

const continueGame = async (gameCode) => {
  try {
    const response = await axios.post(`/api/games/${gameCode}/continue`)
    if (response.data.code === 200) {
      const result = response.data.data
      const progress = result.progress

      // 恢复游戏状态
      currentScore.value = progress.currentScore
      gameLevel.value = progress.gameLevel
      playTime.value = progress.playTime
      gameBoard.value = JSON.parse(progress.gameBoard)
      gameStarted.value = true
      gamePaused.value = false

      // 重新绘制游戏
      drawGame()

      ElMessage.success('游戏已恢复')
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('继续游戏失败:', error)
    ElMessage.error('继续游戏失败')
  }
}

// 象棋游戏方法
const enterChessGame = async () => {
  gameStage.value = 'chess'
  resetChessGameState()

  try {
    const response = await axios.post('/api/chess/start')
    if (response.data.code === 200) {
      const result = response.data.data
      chessGameId.value = result.gameId
      chessBoardState.value = result.boardState
      chessCurrentPlayer.value = result.currentPlayer
      chessGameStatus.value = result.gameStatus
      chessMoveHistory.value = []
      chessGameStartTime.value = Date.now()

      await nextTick()
      initChessCanvas()
      startGameStatePolling()

      ElMessage.success('象棋游戏开始！红方先行')
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('开始象棋游戏失败:', error)
    ElMessage.error('开始象棋游戏失败')
  }
}

const initChessCanvas = () => {
  const canvas = document.querySelector('.chess-canvas')
  if (!canvas) return

  const ctx = canvas.getContext('2d')

  // 设置画布尺寸
  const canvasWidth = 480
  const canvasHeight = 540
  canvas.width = canvasWidth
  canvas.height = canvasHeight

  // 绘制棋盘
  drawChessBoard(ctx, canvasWidth, canvasHeight)

  // 绘制棋子
  if (chessBoardState.value) {
    drawChessPieces(ctx, chessBoardState.value)
  }
}

const drawChessBoard = (ctx, width, height) => {
  const cellWidth = width / 9
  const cellHeight = height / 10
  const margin = Math.min(cellWidth, cellHeight) * 0.1

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 设置棋盘背景
  ctx.fillStyle = '#f4e4bc'
  ctx.fillRect(0, 0, width, height)

  // 绘制棋盘线条
  ctx.strokeStyle = '#8b5a2b'
  ctx.lineWidth = 2

  // 绘制横线 - 修正为在交叉点绘制
  for (let i = 0; i <= 9; i++) {
    const y = i * cellHeight + cellHeight / 2
    ctx.beginPath()
    ctx.moveTo(cellWidth / 2, y)
    ctx.lineTo(width - cellWidth / 2, y)
    ctx.stroke()
  }

  // 绘制竖线 - 修正为在交叉点绘制
  for (let i = 0; i <= 8; i++) {
    const x = i * cellWidth + cellWidth / 2
    ctx.beginPath()
    ctx.moveTo(x, cellHeight / 2)
    ctx.lineTo(x, height - cellHeight / 2)
    ctx.stroke()
  }

  // 绘制楚河汉界
  ctx.fillStyle = '#8b5a2b'
  ctx.font = 'bold 20px serif'
  ctx.textAlign = 'center'
  ctx.fillText('楚河', width * 0.25, height * 0.52)
  ctx.fillText('汉界', width * 0.75, height * 0.52)

  // 绘制九宫格斜线
  drawPalaceLines(ctx, cellWidth, cellHeight, true)  // 红方九宫格
  drawPalaceLines(ctx, cellWidth, cellHeight, false) // 黑方九宫格

  // 绘制炮位和兵位标记
  drawPositionMarkers(ctx, cellWidth, cellHeight)
}

const drawPalaceLines = (ctx, cellWidth, cellHeight, isRed) => {
  ctx.strokeStyle = '#8b5a2b'
  ctx.lineWidth = 2

  if (isRed) {
    // 红方九宫格 (底部)
    const startY = 7 * cellHeight + cellHeight / 2
    const endY = 9 * cellHeight + cellHeight / 2
    const leftX = 3 * cellWidth + cellWidth / 2
    const centerX = 4 * cellWidth + cellWidth / 2
    const rightX = 5 * cellWidth + cellWidth / 2

    // 左上到右下的斜线
    ctx.beginPath()
    ctx.moveTo(leftX, startY)
    ctx.lineTo(rightX, endY)
    ctx.stroke()

    // 右上到左下的斜线
    ctx.beginPath()
    ctx.moveTo(rightX, startY)
    ctx.lineTo(leftX, endY)
    ctx.stroke()
  } else {
    // 黑方九宫格 (顶部)
    const startY = 0 * cellHeight + cellHeight / 2
    const endY = 2 * cellHeight + cellHeight / 2
    const leftX = 3 * cellWidth + cellWidth / 2
    const centerX = 4 * cellWidth + cellWidth / 2
    const rightX = 5 * cellWidth + cellWidth / 2

    // 左上到右下的斜线
    ctx.beginPath()
    ctx.moveTo(leftX, startY)
    ctx.lineTo(rightX, endY)
    ctx.stroke()

    // 右上到左下的斜线
    ctx.beginPath()
    ctx.moveTo(rightX, startY)
    ctx.lineTo(leftX, endY)
    ctx.stroke()
  }
}

const drawPositionMarkers = (ctx, cellWidth, cellHeight) => {
  const markerSize = 6
  ctx.strokeStyle = '#8b5a2b'
  ctx.lineWidth = 1.5

  // 炮位标记 (注意：这里的坐标是[y, x]格式，对应棋盘的[行, 列])
  const cannonPositions = [
    [2, 1], [2, 7], // 黑方炮位
    [7, 1], [7, 7]  // 红方炮位
  ]

  // 兵/卒位标记
  const pawnPositions = [
    [3, 0], [3, 2], [3, 4], [3, 6], [3, 8], // 黑方卒位
    [6, 0], [6, 2], [6, 4], [6, 6], [6, 8]  // 红方兵位
  ]

  const allPositions = [...cannonPositions, ...pawnPositions]

  allPositions.forEach(([row, col]) => {
    const centerX = col * cellWidth + cellWidth / 2
    const centerY = row * cellHeight + cellHeight / 2

    // 绘制四个角的标记
    const corners = [
      [-1, -1], [1, -1], [-1, 1], [1, 1]
    ]

    corners.forEach(([dx, dy]) => {
      const startX = centerX + dx * markerSize
      const startY = centerY + dy * markerSize

      ctx.beginPath()
      ctx.moveTo(startX, startY)
      ctx.lineTo(startX + dx * markerSize * 0.5, startY)
      ctx.moveTo(startX, startY)
      ctx.lineTo(startX, startY + dy * markerSize * 0.5)
      ctx.stroke()
    })
  })
}

const drawChessPieces = (ctx, boardStateJson) => {
  try {
    const board = JSON.parse(boardStateJson)
    const canvasWidth = 480
    const canvasHeight = 540
    const cellWidth = canvasWidth / 9
    const cellHeight = canvasHeight / 10
    const pieceRadius = Math.min(cellWidth, cellHeight) * 0.35

    // 棋子名称映射
    const pieceNames = {
      1: '帅', 2: '仕', 3: '相', 4: '马', 5: '车', 6: '炮', 7: '兵',
      11: '将', 12: '士', 13: '象', 14: '马', 15: '车', 16: '炮', 17: '卒'
    }

    for (let x = 0; x < 10; x++) {
      for (let y = 0; y < 9; y++) {
        const piece = board[x][y]
        if (piece !== 0) {
          // 修正坐标计算：确保棋子在交叉点上
          const centerX = y * cellWidth + cellWidth / 2
          const centerY = x * cellHeight + cellHeight / 2

          // 绘制棋子背景圆
          ctx.fillStyle = piece <= 7 ? '#dc2626' : '#374151'
          ctx.beginPath()
          ctx.arc(centerX, centerY, pieceRadius, 0, 2 * Math.PI)
          ctx.fill()

          // 绘制棋子边框
          ctx.strokeStyle = '#8b5a2b'
          ctx.lineWidth = 3
          ctx.stroke()

          // 绘制棋子内圈
          ctx.fillStyle = '#f4e4bc'
          ctx.beginPath()
          ctx.arc(centerX, centerY, pieceRadius - 4, 0, 2 * Math.PI)
          ctx.fill()

          // 绘制棋子文字
          ctx.fillStyle = piece <= 7 ? '#dc2626' : '#374151'
          ctx.font = 'bold 20px serif'
          ctx.textAlign = 'center'
          ctx.textBaseline = 'middle'
          ctx.fillText(pieceNames[piece] || '', centerX, centerY)

          // 如果是选中的棋子，绘制高亮边框
          if (chessSelectedPiece.value &&
              chessSelectedPiece.value.x === x &&
              chessSelectedPiece.value.y === y) {
            ctx.strokeStyle = '#fbbf24'
            ctx.lineWidth = 4
            ctx.beginPath()
            ctx.arc(centerX, centerY, pieceRadius + 2, 0, 2 * Math.PI)
            ctx.stroke()
          }
        }
      }
    }

    // 绘制可能的移动位置
    if (chessPossibleMoves.value.length > 0) {
      ctx.fillStyle = 'rgba(34, 197, 94, 0.3)'
      chessPossibleMoves.value.forEach(move => {
        const centerX = move.y * cellWidth + cellWidth / 2
        const centerY = move.x * cellHeight + cellHeight / 2
        ctx.beginPath()
        ctx.arc(centerX, centerY, pieceRadius * 0.5, 0, 2 * Math.PI)
        ctx.fill()
      })
    }
  } catch (error) {
    console.error('绘制棋子失败:', error)
  }
}

// 2048游戏核心方法
const initCanvas = () => {
  const canvas = gameCanvas.value
  if (!canvas) return

  // 根据难度设置画布大小
  gameSize.value = gameLevel.value === 1 ? 4 : gameLevel.value === 2 ? 5 : 6
  canvasSize.value = Math.min(400, window.innerWidth - 40)

  canvas.width = canvasSize.value
  canvas.height = canvasSize.value

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
}

const startNewGame = async () => {
  try {
    const response = await axios.post('/api/games/2048/start', {
      level: gameLevel.value
    })

    if (response.data.code === 200) {
      const result = response.data.data
      bestScore.value = result.userStats?.bestScore || 0

      // 初始化游戏
      initGame()
      gameStarted.value = true
      startTime.value = Date.now()

      ElMessage.success('游戏开始！')
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('开始游戏失败:', error)
    ElMessage.error('开始游戏失败')
  }
}

const initGame = () => {
  gameSize.value = gameLevel.value === 1 ? 4 : gameLevel.value === 2 ? 5 : 6
  gameBoard.value = Array(gameSize.value).fill().map(() => Array(gameSize.value).fill(0))
  currentScore.value = 0
  gameOver.value = false
  gameWon.value = false

  // 添加两个初始数字
  addRandomNumber()
  addRandomNumber()

  drawGame()
}

const addRandomNumber = () => {
  const emptyCells = []
  for (let i = 0; i < gameSize.value; i++) {
    for (let j = 0; j < gameSize.value; j++) {
      if (gameBoard.value[i][j] === 0) {
        emptyCells.push({x: i, y: j})
      }
    }
  }

  if (emptyCells.length > 0) {
    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)]
    gameBoard.value[randomCell.x][randomCell.y] = Math.random() < 0.9 ? 2 : 4
  }
}

const drawGame = () => {
  const canvas = gameCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const size = canvasSize.value
  const cellSize = size / gameSize.value
  const padding = cellSize * 0.1

  // 清空画布
  ctx.clearRect(0, 0, size, size)

  // 绘制背景
  ctx.fillStyle = getThemeColor('background')
  ctx.fillRect(0, 0, size, size)

  // 绘制网格和数字
  for (let i = 0; i < gameSize.value; i++) {
    for (let j = 0; j < gameSize.value; j++) {
      const x = j * cellSize + padding
      const y = i * cellSize + padding
      const w = cellSize - padding * 2
      const h = cellSize - padding * 2

      // 绘制单元格背景
      ctx.fillStyle = getThemeColor('cell')
      ctx.fillRect(x, y, w, h)

      // 绘制数字
      const value = gameBoard.value[i][j]
      if (value > 0) {
        ctx.fillStyle = getNumberColor(value)
        ctx.fillRect(x, y, w, h)

        // 绘制数字文本
        ctx.fillStyle = getTextColor(value)
        ctx.font = `bold ${cellSize * 0.3}px Arial`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(value.toString(), x + w/2, y + h/2)
      }
    }
  }
}

const getThemeColor = (type) => {
  const themes = {
    classic: {
      background: '#faf8ef',
      cell: '#cdc1b4'
    },
    colorful: {
      background: '#f0f8ff',
      cell: '#e6f3ff'
    },
    dark: {
      background: '#1a1a1a',
      cell: '#333333'
    }
  }
  return themes[gameTheme.value][type]
}

const getNumberColor = (value) => {
  const colors = {
    2: '#eee4da', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
    32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
    512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
  }
  return colors[value] || '#3c3a32'
}

const getTextColor = (value) => {
  return value <= 4 ? '#776e65' : '#f9f6f2'
}

// 游戏控制方法
const handleKeyDown = (event) => {
  if (!gameStarted.value || gamePaused.value || gameOver.value) return

  let moved = false
  switch (event.key) {
    case 'ArrowUp':
      moved = moveUp()
      break
    case 'ArrowDown':
      moved = moveDown()
      break
    case 'ArrowLeft':
      moved = moveLeft()
      break
    case 'ArrowRight':
      moved = moveRight()
      break
    default:
      return
  }

  if (moved) {
    addRandomNumber()
    drawGame()
    checkGameState()
  }

  event.preventDefault()
}

const moveLeft = () => {
  let moved = false
  for (let i = 0; i < gameSize.value; i++) {
    const row = gameBoard.value[i].filter(val => val !== 0)
    for (let j = 0; j < row.length - 1; j++) {
      if (row[j] === row[j + 1]) {
        row[j] *= 2
        currentScore.value += row[j]
        row.splice(j + 1, 1)
      }
    }
    while (row.length < gameSize.value) {
      row.push(0)
    }

    for (let j = 0; j < gameSize.value; j++) {
      if (gameBoard.value[i][j] !== row[j]) {
        moved = true
      }
      gameBoard.value[i][j] = row[j]
    }
  }
  return moved
}

const moveRight = () => {
  let moved = false
  for (let i = 0; i < gameSize.value; i++) {
    const row = gameBoard.value[i].filter(val => val !== 0)
    for (let j = row.length - 1; j > 0; j--) {
      if (row[j] === row[j - 1]) {
        row[j] *= 2
        currentScore.value += row[j]
        row.splice(j - 1, 1)
        j--
      }
    }
    while (row.length < gameSize.value) {
      row.unshift(0)
    }

    for (let j = 0; j < gameSize.value; j++) {
      if (gameBoard.value[i][j] !== row[j]) {
        moved = true
      }
      gameBoard.value[i][j] = row[j]
    }
  }
  return moved
}

const moveUp = () => {
  let moved = false
  for (let j = 0; j < gameSize.value; j++) {
    const column = []
    for (let i = 0; i < gameSize.value; i++) {
      if (gameBoard.value[i][j] !== 0) {
        column.push(gameBoard.value[i][j])
      }
    }

    for (let i = 0; i < column.length - 1; i++) {
      if (column[i] === column[i + 1]) {
        column[i] *= 2
        currentScore.value += column[i]
        column.splice(i + 1, 1)
      }
    }

    while (column.length < gameSize.value) {
      column.push(0)
    }

    for (let i = 0; i < gameSize.value; i++) {
      if (gameBoard.value[i][j] !== column[i]) {
        moved = true
      }
      gameBoard.value[i][j] = column[i]
    }
  }
  return moved
}

const moveDown = () => {
  let moved = false
  for (let j = 0; j < gameSize.value; j++) {
    const column = []
    for (let i = 0; i < gameSize.value; i++) {
      if (gameBoard.value[i][j] !== 0) {
        column.push(gameBoard.value[i][j])
      }
    }

    for (let i = column.length - 1; i > 0; i--) {
      if (column[i] === column[i - 1]) {
        column[i] *= 2
        currentScore.value += column[i]
        column.splice(i - 1, 1)
        i--
      }
    }

    while (column.length < gameSize.value) {
      column.unshift(0)
    }

    for (let i = 0; i < gameSize.value; i++) {
      if (gameBoard.value[i][j] !== column[i]) {
        moved = true
      }
      gameBoard.value[i][j] = column[i]
    }
  }
  return moved
}

const checkGameState = () => {
  // 检查是否获胜
  for (let i = 0; i < gameSize.value; i++) {
    for (let j = 0; j < gameSize.value; j++) {
      if (gameBoard.value[i][j] === 2048) {
        gameWon.value = true
        gameOver.value = true
        finishGame(true)
        return
      }
    }
  }

  // 检查是否还能移动
  if (!canMove()) {
    gameOver.value = true
    finishGame(false)
  }
}

const canMove = () => {
  // 检查是否有空格
  for (let i = 0; i < gameSize.value; i++) {
    for (let j = 0; j < gameSize.value; j++) {
      if (gameBoard.value[i][j] === 0) {
        return true
      }
    }
  }

  // 检查是否有相邻相同数字
  for (let i = 0; i < gameSize.value; i++) {
    for (let j = 0; j < gameSize.value; j++) {
      const current = gameBoard.value[i][j]
      if ((i < gameSize.value - 1 && gameBoard.value[i + 1][j] === current) ||
          (j < gameSize.value - 1 && gameBoard.value[i][j + 1] === current)) {
        return true
      }
    }
  }

  return false
}

// 游戏控制方法
const pauseGame = async () => {
  if (!gameStarted.value || gamePaused.value) return

  gamePaused.value = true
  playTime.value += Math.floor((Date.now() - startTime.value) / 1000)

  try {
    await axios.post('/api/games/2048/pause', {
      gameBoard: JSON.stringify(gameBoard.value),
      currentScore: currentScore.value,
      playTime: playTime.value
    })
    ElMessage.success('游戏已暂停')
  } catch (error) {
    console.error('暂停游戏失败:', error)
  }
}

const resumeGame = () => {
  if (!gamePaused.value) return

  gamePaused.value = false
  startTime.value = Date.now()
  ElMessage.success('游戏已恢复')
}

const restartGame = () => {
  ElMessageBox.confirm('确定要重新开始游戏吗？当前进度将丢失。', '重新开始', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    resetGameState()
    startNewGame()
  })
}

const finishGame = async (isCompleted) => {
  gameStarted.value = false
  playTime.value += Math.floor((Date.now() - startTime.value) / 1000)

  try {
    const response = await axios.post('/api/games/2048/finish', {
      finalScore: currentScore.value,
      gameData: JSON.stringify(gameBoard.value),
      playTime: playTime.value,
      isCompleted: isCompleted
    })

    if (response.data.code === 200) {
      const result = response.data.data
      isNewRecord.value = result.isNewRecord
      bestScore.value = result.userStats.bestScore

      if (isNewRecord.value) {
        ElMessage.success('🏆 恭喜！创造了新纪录！')
      }
    }
  } catch (error) {
    console.error('保存游戏结果失败:', error)
  }
}

// 触摸事件处理
const handleTouchStart = (event) => {
  if (!gameStarted.value || gamePaused.value || gameOver.value) return

  const touch = event.touches[0]
  touchStartX.value = touch.clientX
  touchStartY.value = touch.clientY
  event.preventDefault()
}

const handleTouchMove = (event) => {
  event.preventDefault()
}

const handleTouchEnd = (event) => {
  if (!gameStarted.value || gamePaused.value || gameOver.value) return

  const touch = event.changedTouches[0]
  const deltaX = touch.clientX - touchStartX.value
  const deltaY = touch.clientY - touchStartY.value
  const minSwipeDistance = 30

  if (Math.abs(deltaX) < minSwipeDistance && Math.abs(deltaY) < minSwipeDistance) {
    return
  }

  let moved = false
  if (Math.abs(deltaX) > Math.abs(deltaY)) {
    // 水平滑动
    if (deltaX > 0) {
      moved = moveRight()
    } else {
      moved = moveLeft()
    }
  } else {
    // 垂直滑动
    if (deltaY > 0) {
      moved = moveDown()
    } else {
      moved = moveUp()
    }
  }

  if (moved) {
    addRandomNumber()
    drawGame()
    checkGameState()
  }

  event.preventDefault()
}

// 游戏设置方法
const onLevelChange = () => {
  if (gameStarted.value) {
    ElMessage.warning('游戏进行中，无法更改难度')
    return
  }
  initCanvas()
}

const onThemeChange = () => {
  if (gameStarted.value) {
    drawGame()
  }
}

const showHint = () => {
  if (!gameStarted.value || gamePaused.value || gameOver.value) return

  // 简单的提示逻辑：显示可能的移动方向
  const possibleMoves = []

  // 测试每个方向是否可以移动
  const originalBoard = JSON.parse(JSON.stringify(gameBoard.value))

  if (testMove('left')) possibleMoves.push('←')
  if (testMove('right')) possibleMoves.push('→')
  if (testMove('up')) possibleMoves.push('↑')
  if (testMove('down')) possibleMoves.push('↓')

  // 恢复原始棋盘
  gameBoard.value = originalBoard

  if (possibleMoves.length > 0) {
    ElMessage.info(`可以向这些方向移动: ${possibleMoves.join(' ')}`)
  } else {
    ElMessage.warning('没有可用的移动方向')
  }
}

const testMove = (direction) => {
  switch (direction) {
    case 'left': return moveLeft()
    case 'right': return moveRight()
    case 'up': return moveUp()
    case 'down': return moveDown()
    default: return false
  }
}

// 象棋游戏辅助方法
const formatGameTime = () => {
  if (chessGameStartTime.value === 0) return '00:00'
  const elapsed = Math.floor((Date.now() - chessGameStartTime.value) / 1000)
  const minutes = Math.floor(elapsed / 60)
  const seconds = elapsed % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

const getGameEndMessage = () => {
  if (chessGameStatus.value === 'CHECKMATE') {
    return `🎉 ${chessWinner.value === 'RED' ? '红方' : '黑方'}获胜！`
  } else if (chessGameStatus.value === 'SURRENDER') {
    return `${chessWinner.value === 'RED' ? '红方' : '黑方'}获胜！对方认输`
  }
  return '游戏结束'
}

const handleChessClick = async (event) => {
  if (chessGameStatus.value !== 'PLAYING') return

  const canvas = event.target
  const rect = canvas.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 计算点击的棋盘位置 - 修正坐标计算
  const cellWidth = 480 / 9
  const cellHeight = 540 / 10
  const boardX = Math.round((y - cellHeight / 2) / cellHeight)
  const boardY = Math.round((x - cellWidth / 2) / cellWidth)

  // 检查点击位置是否在棋盘范围内
  if (boardX < 0 || boardX >= 10 || boardY < 0 || boardY >= 9) return

  try {
    const board = JSON.parse(chessBoardState.value)
    const clickedPiece = board[boardX][boardY]

    if (chessSelectedPiece.value === null) {
      // 没有选中棋子，尝试选择棋子
      if (clickedPiece !== 0 && isPieceOfCurrentPlayer(clickedPiece)) {
        chessSelectedPiece.value = { x: boardX, y: boardY }
        // 获取可能的移动位置
        chessPossibleMoves.value = getPossibleMoves(board, boardX, boardY)
        redrawChessBoard()
        playMoveSound()
      }
    } else {
      // 已选中棋子，尝试移动
      const fromX = chessSelectedPiece.value.x
      const fromY = chessSelectedPiece.value.y

      if (fromX === boardX && fromY === boardY) {
        // 点击同一个棋子，取消选择
        chessSelectedPiece.value = null
        chessPossibleMoves.value = []
        redrawChessBoard()
      } else {
        // 尝试移动棋子
        await movePiece(fromX, fromY, boardX, boardY)
      }
    }
  } catch (error) {
    console.error('处理棋子点击失败:', error)
  }
}

const isPieceOfCurrentPlayer = (piece) => {
  if (chessCurrentPlayer.value === 1) {
    return piece >= 1 && piece <= 7 // 红方棋子
  } else {
    return piece >= 11 && piece <= 17 // 黑方棋子
  }
}

const getPossibleMoves = (board, fromX, fromY) => {
  const moves = []
  const piece = board[fromX][fromY]

  // 根据棋子类型计算可能的移动位置
  switch (piece) {
    case 1: case 11: // 帅/将
      getPossibleKingMoves(board, fromX, fromY, moves)
      break
    case 2: case 12: // 仕/士
      getPossibleAdvisorMoves(board, fromX, fromY, moves)
      break
    case 3: case 13: // 相/象
      getPossibleBishopMoves(board, fromX, fromY, moves)
      break
    case 4: case 14: // 马
      getPossibleKnightMoves(board, fromX, fromY, moves)
      break
    case 5: case 15: // 车
      getPossibleRookMoves(board, fromX, fromY, moves)
      break
    case 6: case 16: // 炮
      getPossibleCannonMoves(board, fromX, fromY, moves)
      break
    case 7: case 17: // 兵/卒
      getPossiblePawnMoves(board, fromX, fromY, moves)
      break
  }

  return moves
}

const getPossibleKingMoves = (board, fromX, fromY, moves) => {
  const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
  const piece = board[fromX][fromY]
  const isRed = piece <= 7

  directions.forEach(([dx, dy]) => {
    const newX = fromX + dx
    const newY = fromY + dy

    // 检查是否在九宫格内
    if (isInPalace(newX, newY, isRed) && !isPieceOfSamePlayer(piece, board[newX][newY])) {
      moves.push({ x: newX, y: newY })
    }
  })
}

const getPossibleAdvisorMoves = (board, fromX, fromY, moves) => {
  const directions = [[-1, -1], [-1, 1], [1, -1], [1, 1]]
  const piece = board[fromX][fromY]
  const isRed = piece <= 7

  directions.forEach(([dx, dy]) => {
    const newX = fromX + dx
    const newY = fromY + dy

    if (isInPalace(newX, newY, isRed) && !isPieceOfSamePlayer(piece, board[newX][newY])) {
      moves.push({ x: newX, y: newY })
    }
  })
}

const getPossibleBishopMoves = (board, fromX, fromY, moves) => {
  const directions = [[-2, -2], [-2, 2], [2, -2], [2, 2]]
  const piece = board[fromX][fromY]
  const isRed = piece <= 7

  directions.forEach(([dx, dy]) => {
    const newX = fromX + dx
    const newY = fromY + dy

    // 检查是否越河和象眼是否被堵
    if (isValidPosition(newX, newY) &&
        !isBishopCrossRiver(newX, isRed) &&
        board[fromX + dx/2][fromY + dy/2] === 0 &&
        !isPieceOfSamePlayer(piece, board[newX][newY])) {
      moves.push({ x: newX, y: newY })
    }
  })
}

const getPossibleKnightMoves = (board, fromX, fromY, moves) => {
  const knightMoves = [
    [-2, -1], [-2, 1], [-1, -2], [-1, 2],
    [1, -2], [1, 2], [2, -1], [2, 1]
  ]
  const piece = board[fromX][fromY]

  knightMoves.forEach(([dx, dy]) => {
    const newX = fromX + dx
    const newY = fromY + dy

    if (isValidPosition(newX, newY) && !isPieceOfSamePlayer(piece, board[newX][newY])) {
      // 检查马腿是否被堵
      const legX = Math.abs(dx) === 2 ? fromX + dx/2 : fromX
      const legY = Math.abs(dy) === 2 ? fromY + dy/2 : fromY

      if (board[legX][legY] === 0) {
        moves.push({ x: newX, y: newY })
      }
    }
  })
}

const getPossibleRookMoves = (board, fromX, fromY, moves) => {
  const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
  const piece = board[fromX][fromY]

  directions.forEach(([dx, dy]) => {
    for (let i = 1; i < 10; i++) {
      const newX = fromX + dx * i
      const newY = fromY + dy * i

      if (!isValidPosition(newX, newY)) break

      if (board[newX][newY] === 0) {
        moves.push({ x: newX, y: newY })
      } else {
        if (!isPieceOfSamePlayer(piece, board[newX][newY])) {
          moves.push({ x: newX, y: newY })
        }
        break
      }
    }
  })
}

const getPossibleCannonMoves = (board, fromX, fromY, moves) => {
  const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
  const piece = board[fromX][fromY]

  directions.forEach(([dx, dy]) => {
    let foundPlatform = false

    for (let i = 1; i < 10; i++) {
      const newX = fromX + dx * i
      const newY = fromY + dy * i

      if (!isValidPosition(newX, newY)) break

      if (!foundPlatform) {
        if (board[newX][newY] === 0) {
          moves.push({ x: newX, y: newY })
        } else {
          foundPlatform = true
        }
      } else {
        if (board[newX][newY] !== 0) {
          if (!isPieceOfSamePlayer(piece, board[newX][newY])) {
            moves.push({ x: newX, y: newY })
          }
          break
        }
      }
    }
  })
}

const getPossiblePawnMoves = (board, fromX, fromY, moves) => {
  const piece = board[fromX][fromY]
  const isRed = piece <= 7

  // 向前移动
  const forwardX = isRed ? fromX - 1 : fromX + 1
  if (isValidPosition(forwardX, fromY) && !isPieceOfSamePlayer(piece, board[forwardX][fromY])) {
    moves.push({ x: forwardX, y: fromY })
  }

  // 过河后可以横移
  if ((isRed && fromX < 5) || (!isRed && fromX > 4)) {
    const leftY = fromY - 1
    const rightY = fromY + 1

    if (isValidPosition(fromX, leftY) && !isPieceOfSamePlayer(piece, board[fromX][leftY])) {
      moves.push({ x: fromX, y: leftY })
    }

    if (isValidPosition(fromX, rightY) && !isPieceOfSamePlayer(piece, board[fromX][rightY])) {
      moves.push({ x: fromX, y: rightY })
    }
  }
}

// 辅助函数
const isValidPosition = (x, y) => {
  return x >= 0 && x < 10 && y >= 0 && y < 9
}

const isInPalace = (x, y, isRed) => {
  if (isRed) {
    return x >= 7 && x <= 9 && y >= 3 && y <= 5
  } else {
    return x >= 0 && x <= 2 && y >= 3 && y <= 5
  }
}

const isBishopCrossRiver = (x, isRed) => {
  if (isRed) {
    return x < 5
  } else {
    return x > 4
  }
}

const isPieceOfSamePlayer = (piece1, piece2) => {
  return (piece1 <= 7 && piece2 <= 7) || (piece1 >= 11 && piece2 >= 11)
}

const movePiece = async (fromX, fromY, toX, toY) => {
  try {
    const response = await axios.post('/api/chess/move', {
      gameId: chessGameId.value,
      fromX: fromX,
      fromY: fromY,
      toX: toX,
      toY: toY
    })

    if (response.data.code === 200) {
      const result = response.data.data

      // 更新游戏状态
      chessBoardState.value = result.boardState
      chessCurrentPlayer.value = result.currentPlayer
      chessGameStatus.value = result.gameStatus
      chessMoveHistory.value = result.moveHistory || []
      chessInCheck.value = result.inCheck || false

      if (result.winner) {
        chessWinner.value = result.winner
      }

      // 清除选择状态
      chessSelectedPiece.value = null
      chessPossibleMoves.value = []

      // 重新绘制棋盘
      redrawChessBoard()

      // 播放音效
      if (result.capturedPiece && result.capturedPiece !== 0) {
        playCaptureSound()
      } else {
        playMoveSound()
      }

      // 检查游戏状态
      if (result.checkmate) {
        playCheckSound()
        ElMessage.success(result.message)
      } else if (result.inCheck) {
        playCheckSound()
        ElMessage.warning('将军！')
      }

    } else {
      ElMessage.error(response.data.message)
      // 移动失败，清除选择
      chessSelectedPiece.value = null
      chessPossibleMoves.value = []
      redrawChessBoard()
    }
  } catch (error) {
    console.error('移动棋子失败:', error)
    ElMessage.error('移动棋子失败')
    chessSelectedPiece.value = null
    chessPossibleMoves.value = []
    redrawChessBoard()
  }
}

const redrawChessBoard = () => {
  const canvas = document.querySelector('.chess-canvas')
  if (canvas) {
    const ctx = canvas.getContext('2d')
    drawChessBoard(ctx, 480, 540)
    if (chessBoardState.value) {
      drawChessPieces(ctx, chessBoardState.value)
    }
  }
}

// 音效系统
const playMoveSound = () => {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1)

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1)

    oscillator.start(audioContext.currentTime)
    oscillator.stop(audioContext.currentTime + 0.1)
  } catch (error) {
    // 音效播放失败，静默处理
  }
}

const playCaptureSound = () => {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)

    oscillator.frequency.setValueAtTime(600, audioContext.currentTime)
    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.2)

    gainNode.gain.setValueAtTime(0.4, audioContext.currentTime)
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)

    oscillator.start(audioContext.currentTime)
    oscillator.stop(audioContext.currentTime + 0.2)
  } catch (error) {
    // 音效播放失败，静默处理
  }
}

const playCheckSound = () => {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    // 播放三次短促的提示音
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()

        oscillator.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.setValueAtTime(1000, audioContext.currentTime)
        gainNode.gain.setValueAtTime(0.5, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1)

        oscillator.start(audioContext.currentTime)
        oscillator.stop(audioContext.currentTime + 0.1)
      }, i * 150)
    }
  } catch (error) {
    // 音效播放失败，静默处理
  }
}

const requestUndo = async () => {
  try {
    const response = await axios.post('/api/chess/undo/request', {
      gameId: chessGameId.value
    })
    if (response.data.code === 200) {
      ElMessage.success('悔棋请求已发送')
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    ElMessage.error('悔棋请求失败')
  }
}

const respondUndo = async (accept) => {
  try {
    const response = await axios.post('/api/chess/undo/respond', {
      gameId: chessGameId.value,
      accept: accept
    })
    if (response.data.code === 200) {
      if (accept) {
        // 更新游戏状态
        const result = response.data.data
        chessBoardState.value = result.boardState
        chessCurrentPlayer.value = result.currentPlayer
        chessMoveHistory.value = result.moveHistory || []

        // 重新绘制棋盘
        redrawChessBoard()
      }
      chessUndoRequested.value = false
      chessUndoRequestPlayer.value = 0
      ElMessage.success(response.data.data.message)
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    ElMessage.error('处理悔棋失败')
  }
}

const surrenderGame = async () => {
  try {
    await ElMessageBox.confirm('确定要认输吗？', '认输确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await axios.post('/api/chess/surrender', {
      gameId: chessGameId.value
    })
    if (response.data.code === 200) {
      const result = response.data.data
      chessGameStatus.value = result.gameStatus
      chessWinner.value = result.winner
      ElMessage.success(result.message)
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('认输失败')
    }
  }
}

const saveChessGame = async () => {
  try {
    const { value: gameName } = await ElMessageBox.prompt('请输入棋局名称', '保存棋局', {
      confirmButtonText: '保存',
      cancelButtonText: '取消',
      inputValue: '对局记录'
    })

    const response = await axios.post('/api/chess/save', {
      gameId: chessGameId.value,
      gameName: gameName
    })
    if (response.data.code === 200) {
      ElMessage.success('棋局保存成功')
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('保存棋局失败')
    }
  }
}

const showLoadDialog = async () => {
  try {
    // 获取用户的象棋游戏记录
    const response = await axios.get('/api/chess/records?limit=10')
    if (response.data.code === 200) {
      const records = response.data.data

      if (records.length === 0) {
        ElMessage.info('暂无保存的棋局记录')
        return
      }

      // 构建选项列表
      const options = records.map(record => ({
        label: `${record.gameName || '未命名棋局'} - ${formatDate(record.createdTime)}`,
        value: record.id
      }))

      // 显示选择对话框
      const { value: selectedId } = await ElMessageBox.prompt('选择要加载的棋局', '加载棋局', {
        confirmButtonText: '加载',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: options
      })

      if (selectedId) {
        await loadChessGame(selectedId)
      }
    } else {
      ElMessage.error('获取棋局记录失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('加载棋局失败')
    }
  }
}

const loadChessGame = async (gameRecordId) => {
  try {
    const response = await axios.post('/api/chess/load', {
      gameRecordId: gameRecordId
    })

    if (response.data.code === 200) {
      const result = response.data.data

      // 更新游戏状态
      chessGameId.value = result.gameId
      chessBoardState.value = result.boardState
      chessCurrentPlayer.value = result.currentPlayer
      chessGameStatus.value = result.gameStatus
      chessMoveHistory.value = result.moveHistory || []
      chessGameStartTime.value = Date.now()

      // 重新绘制棋盘
      redrawChessBoard()
      startGameStatePolling()

      ElMessage.success('棋局加载成功')
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    ElMessage.error('加载棋局失败')
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

const startNewChessGame = () => {
  enterChessGame()
}

// 游戏状态轮询
let gameStateInterval = null

const startGameStatePolling = () => {
  if (gameStateInterval) {
    clearInterval(gameStateInterval)
  }

  gameStateInterval = setInterval(async () => {
    if (chessGameId.value && chessGameStatus.value === 'PLAYING') {
      try {
        const response = await axios.get(`/api/chess/state/${chessGameId.value}`)
        if (response.data.code === 200) {
          const result = response.data.data

          // 检查是否有悔棋请求
          if (result.undoRequested !== chessUndoRequested.value) {
            chessUndoRequested.value = result.undoRequested
            chessUndoRequestPlayer.value = result.undoRequestPlayer

            if (result.undoRequested) {
              ElMessage.info(`${result.undoRequestPlayer === 1 ? '红方' : '黑方'}请求悔棋`)
            }
          }

          // 检查游戏状态变化
          if (result.gameStatus !== chessGameStatus.value) {
            chessGameStatus.value = result.gameStatus
            if (result.winner) {
              chessWinner.value = result.winner
            }
          }
        }
      } catch (error) {
        // 轮询失败，静默处理
      }
    }
  }, 2000) // 每2秒轮询一次
}

const stopGameStatePolling = () => {
  if (gameStateInterval) {
    clearInterval(gameStateInterval)
    gameStateInterval = null
  }
}

// 重置象棋游戏状态
const resetChessGameState = () => {
  chessGameId.value = ''
  chessBoardState.value = ''
  chessCurrentPlayer.value = 1
  chessGameStatus.value = 'PLAYING'
  chessMoveHistory.value = []
  chessUndoRequested.value = false
  chessUndoRequestPlayer.value = 0
  chessWinner.value = ''
  chessGameStartTime.value = 0
  chessSelectedPiece.value = null
  chessPossibleMoves.value = []
  chessInCheck.value = false
  stopGameStatePolling()
}

// 更新返回游戏列表方法
const backToGameList = () => {
  gameStage.value = 'list'
  resetGameState()
  resetChessGameState()
}

// ==================== 转盘游戏相关方法 ====================

// 进入转盘游戏
const enterWheelGame = async () => {
  gameStage.value = 'wheel'
  wheelStage.value = 'types'

  // 加载转盘类型
  try {
    const response = await axios.get('/api/wheel/types')
    if (response.data.code === 200) {
      wheelTypes.value = response.data.data
    }
  } catch (error) {
    console.error('加载转盘类型失败:', error)
    ElMessage.error('加载转盘类型失败')
  }
}

// 返回转盘类型选择
const backToWheelTypes = () => {
  wheelStage.value = 'types'
  selectedWheelType.value = ''
  wheelConfigs.value = []
}

// 选择转盘类型
const selectWheelType = async (wheelType) => {
  selectedWheelType.value = wheelType
  wheelStage.value = 'wheel'

  // 加载转盘配置
  await loadWheelConfig(wheelType)
}

// 加载转盘配置
const loadWheelConfig = async (wheelType) => {
  try {
    const response = await axios.get(`/api/wheel/${wheelType}/config`)
    if (response.data.code === 200) {
      wheelConfigs.value = response.data.data.filter(config => config.content && config.content.trim())
    }
  } catch (error) {
    console.error('加载转盘配置失败:', error)
    ElMessage.error('加载转盘配置失败')
  }
}

// 转盘旋转
const spinWheel = async () => {
  if (isSpinning.value || wheelConfigs.value.length === 0) return

  isSpinning.value = true

  try {
    const response = await axios.post(`/api/wheel/${selectedWheelType.value}/spin`)
    if (response.data.code === 200) {
      const result = response.data.data
      spinResult.value = result.result

      // 计算转盘旋转角度
      const targetIndex = wheelConfigs.value.findIndex(config => config.content === result.result)
      const sliceAngle = 360 / wheelConfigs.value.length
      const targetAngle = targetIndex * sliceAngle + sliceAngle / 2
      const randomSpins = 5 + Math.random() * 3 // 5-8圈，增加转动圈数
      const finalRotation = randomSpins * 360 + targetAngle

      // 设置CSS变量用于动画
      const wheelElement = document.querySelector('.wheel-disc')
      if (wheelElement) {
        wheelElement.style.setProperty('--final-rotation', `${finalRotation}deg`)
        wheelRotation.value = finalRotation
      }

      // 5秒后显示结果
      setTimeout(() => {
        isSpinning.value = false
        showResultDialog.value = true
      }, 5000)

    } else {
      isSpinning.value = false
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    isSpinning.value = false
    console.error('转盘旋转失败:', error)
    ElMessage.error('转盘旋转失败')
  }
}

// 再转一次
const spinAgain = () => {
  showResultDialog.value = false
  setTimeout(() => {
    spinWheel()
  }, 500)
}

// 获取转盘类型名称
const getWheelTypeName = (wheelType) => {
  const typeMap = {
    'eat': '🍽️ 吃什么',
    'play': '🎮 玩什么',
    'do': '📝 干什么'
  }
  return typeMap[wheelType] || wheelType
}

// 获取扇形样式
const getSliceStyle = (index) => {
  const total = wheelConfigs.value.length
  const angle = 360 / total
  const rotation = index * angle

  return {
    transform: `rotate(${rotation}deg)`,
    '--slice-angle': `${angle}deg`,
    '--slice-color': getSliceColor(index)
  }
}

// 获取扇形颜色
const getSliceColor = (index) => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F8C471', '#82E0AA'
  ]
  return colors[index % colors.length]
}

// 编辑转盘配置
const editWheelConfig = () => {
  // 初始化编辑数据（12个格子）
  editConfigs.value = []
  for (let i = 0; i < 12; i++) {
    const existingConfig = wheelConfigs.value.find(config => config.slotIndex === i)
    editConfigs.value.push({
      slotIndex: i,
      content: existingConfig ? existingConfig.content : '',
      weight: existingConfig ? existingConfig.weight : 100
    })
  }
  showEditDialog.value = true
}

// 保存转盘配置
const saveWheelConfig = async () => {
  try {
    // 过滤有效配置
    const validConfigs = editConfigs.value.filter(config => config.content && config.content.trim())

    if (validConfigs.length === 0) {
      ElMessage.error('至少需要一个有效的转盘选项')
      return
    }

    const response = await axios.post(`/api/wheel/${selectedWheelType.value}/config`, validConfigs)
    if (response.data.code === 200) {
      ElMessage.success('保存成功')
      showEditDialog.value = false
      // 重新加载配置
      await loadWheelConfig(selectedWheelType.value)
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('保存转盘配置失败:', error)
    ElMessage.error('保存转盘配置失败')
  }
}

// 重置转盘配置
const resetWheelConfig = async () => {
  try {
    const response = await axios.post(`/api/wheel/${selectedWheelType.value}/reset`)
    if (response.data.code === 200) {
      ElMessage.success('重置成功')
      showEditDialog.value = false
      // 重新加载配置
      await loadWheelConfig(selectedWheelType.value)
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('重置转盘配置失败:', error)
    ElMessage.error('重置转盘配置失败')
  }
}

// 加载转盘记录
const loadWheelRecords = async () => {
  try {
    const response = await axios.get('/api/wheel/records', {
      params: {
        page: wheelRecordsPage.value,
        size: wheelRecordsSize.value
      }
    })
    if (response.data.code === 200) {
      const result = response.data.data
      wheelRecords.value = result.records
      wheelRecordsTotal.value = result.total
    }
  } catch (error) {
    console.error('加载转盘记录失败:', error)
    ElMessage.error('加载转盘记录失败')
  }
}

// 清空转盘记录
const clearWheelRecords = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有转盘记录吗？', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await axios.delete('/api/wheel/records')
    if (response.data.code === 200) {
      ElMessage.success('清空成功')
      wheelRecords.value = []
      wheelRecordsTotal.value = 0
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空转盘记录失败:', error)
      ElMessage.error('清空转盘记录失败')
    }
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 监听编辑对话框打开
const handleEditDialogOpen = () => {
  editWheelConfig()
}

// 监听历史记录对话框打开
const handleRecordsDialogOpen = () => {
  showRecordsDialog.value = true
  wheelRecordsPage.value = 1
  loadWheelRecords()
}

// 生命周期
onMounted(() => {
  loadGameList()
})

// 初始化游戏列表数据（包含转盘游戏）
const initGameList = () => {
  gameList.value = [
    {
      id: 1,
      gameCode: '2048',
      gameName: '2048',
      gameDescription: '经典数字合成游戏，挑战你的逻辑思维！',
      gameIcon: 'https://api.dicebear.com/7.x/shapes/svg?seed=2048&backgroundColor=f59e0b'
    },
    {
      id: 2,
      gameCode: 'chess',
      gameName: '中国象棋',
      gameDescription: '经典中国象棋，支持双人对战、悔棋、保存棋局等功能。',
      gameIcon: 'https://api.dicebear.com/7.x/shapes/svg?seed=chess&backgroundColor=8b5cf6'
    },
    {
      id: 3,
      gameCode: 'wheel',
      gameName: '转盘游戏',
      gameDescription: '随机转盘，帮你做选择！支持自定义内容和权重。',
      gameIcon: 'https://api.dicebear.com/7.x/shapes/svg?seed=wheel&backgroundColor=e74c3c'
    }
  ]
}
</script>

<style scoped>
/* 游戏容器样式 */
.games-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.games-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.games-content {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.games-header {
  text-align: center;
  margin-bottom: 30px;
}

.games-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.games-header p {
  color: #606266;
  font-size: 16px;
}

.game-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.game-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
  display: flex;
  gap: 15px;
}

.game-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.game-icon {
  flex-shrink: 0;
}

.game-icon img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.game-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.game-info h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.game-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  margin: 0;
}

.game-stats {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
}

.game-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* 2048游戏样式 */
.game-2048 {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-right {
  display: flex;
  gap: 20px;
}

.game-score, .best-score {
  text-align: center;
  background: #409eff;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  min-width: 80px;
}

.score-label {
  display: block;
  font-size: 12px;
  opacity: 0.8;
}

.score-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
}

.game-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.control-left, .control-right {
  display: flex;
  gap: 10px;
  align-items: center;
}

.game-canvas-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.game-canvas {
  border: 2px solid #bbada0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.game-canvas.classic {
  background-color: #faf8ef;
}

.game-canvas.colorful {
  background-color: #f0f8ff;
}

.game-canvas.dark {
  background-color: #1a1a1a;
  border-color: #444;
}

.game-canvas.paused {
  filter: blur(2px);
  opacity: 0.7;
}

.pause-overlay, .game-over-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.pause-content, .game-over-content {
  text-align: center;
  color: white;
  padding: 20px;
}

.pause-content h3, .game-over-content h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.pause-content p, .game-over-content p {
  margin: 5px 0;
  opacity: 0.9;
}

.new-record {
  color: #f59e0b !important;
  font-weight: 600;
  font-size: 16px;
}

.game-over-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.game-instructions {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.game-instructions h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.game-instructions p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

.key-hints {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.key {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.hint-text {
  color: #606266;
  font-size: 14px;
}

.back-button {
  color: #409eff !important;
  font-size: 14px;
}

.back-button:hover {
  color: #66b1ff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-grid {
    grid-template-columns: 1fr;
  }

  .game-card {
    flex-direction: column;
    text-align: center;
  }

  .game-2048 {
    padding: 15px;
  }

  .game-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .header-right {
    align-self: stretch;
    justify-content: space-between;
  }

  .game-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .control-left, .control-right {
    justify-content: center;
    flex-wrap: wrap;
  }

  .game-canvas {
    max-width: 100%;
  }

  .key-hints {
    flex-wrap: wrap;
  }
}

/* 象棋游戏样式 */
.chess-game {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.chess-game::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #dc2626, #b91c1c, #991b1b);
}

.chess-main {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
}

.chess-board-container {
  position: relative;
  flex-shrink: 0;
}

.chess-canvas {
  border: 2px solid #8b5a2b;
  border-radius: 8px;
  background: #f4e4bc;
  cursor: pointer;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.check-alert {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc2626;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.undo-request {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
}

.undo-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.chess-sidebar {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 300px;
}

.chess-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.chess-controls .el-button {
  flex: 1;
  min-width: 80px;
}

.game-timer, .current-player {
  text-align: center;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px 12px;
  border-radius: 8px;
  min-width: 80px;
}

.timer-label, .player-label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 2px;
}

.timer-value, .player-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
}

.red-player {
  color: #dc2626;
}

.black-player {
  color: #374151;
}

.chess-notation {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 15px;
  flex: 1;
}

.chess-notation h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 16px;
}

.notation-list {
  max-height: 200px;
  overflow-y: auto;
}

.notation-item {
  display: flex;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid rgba(108, 117, 125, 0.1);
}

.move-number {
  color: #6c757d;
  font-weight: 600;
  min-width: 30px;
}

.move-text {
  color: #495057;
}

.chess-instructions {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 15px;
}

.chess-instructions h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 16px;
}

.chess-instructions p {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.chess-instructions ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
  font-size: 14px;
}

.chess-instructions li {
  margin-bottom: 5px;
}

/* 象棋响应式设计 */
@media (max-width: 768px) {
  .chess-main {
    flex-direction: column;
    padding: 15px;
  }

  .chess-sidebar {
    max-width: none;
  }

  .chess-canvas {
    max-width: 100%;
    height: auto;
  }

  .chess-controls {
    justify-content: center;
  }

  .notation-list {
    max-height: 150px;
  }
}

/* ==================== 转盘游戏样式 ==================== */
.wheel-game {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

/* 转盘类型选择 */
.wheel-types-content {
  flex: 1;
  padding: 20px;
}

.wheel-types-header {
  text-align: center;
  margin-bottom: 30px;
}

.wheel-types-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.wheel-types-header p {
  color: #606266;
  font-size: 16px;
}

.wheel-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.wheel-type-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.wheel-type-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.wheel-type-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.wheel-type-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.wheel-type-card p {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  margin: 0 0 20px 0;
}

/* 转盘游戏界面 */
.wheel-spin {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.wheel-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.wheel-wrapper {
  position: relative;
  width: 400px;
  height: 400px;
  margin-bottom: 30px;
}

.wheel-disc {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --final-rotation: 0deg;
}

.wheel-disc.spinning {
  /* 5秒平滑转盘动画：先加速后减速，类似真实转盘 */
  animation: wheelSpinning 5s cubic-bezier(0.25, 0.1, 0.75, 0.9) forwards;
}

/* 转盘旋转动画关键帧 - 平滑的加速减速效果 */
@keyframes wheelSpinning {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(var(--final-rotation));
  }
}

.wheel-slice {
  position: absolute;
  width: 50%;
  height: 50%;
  top: 50%;
  left: 50%;
  transform-origin: 0 0;
  background: var(--slice-color);
  clip-path: polygon(0 0, 100% 0, 50% 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.wheel-text {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translateX(-50%) rotate(calc(var(--slice-angle) / 2));
  color: white;
  font-weight: 600;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.wheel-pointer {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 30px solid #e74c3c;
  z-index: 10;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.wheel-controls {
  text-align: center;
}

/* 转盘结果对话框 */
.result-content {
  text-align: center;
  padding: 20px;
}

.result-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.result-content h3 {
  color: #409eff;
  font-size: 24px;
  margin: 0 0 10px 0;
}

.result-content p {
  color: #606266;
  margin: 0;
}

.result-actions {
  text-align: center;
}

/* 编辑对话框 */
.edit-content {
  padding: 10px 0;
}

.edit-tips {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.edit-tips p {
  margin: 0 0 10px 0;
  color: #409eff;
  font-weight: 600;
}

.edit-tips ul {
  margin: 0;
  padding-left: 20px;
}

.edit-tips li {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 5px;
}

.edit-actions {
  text-align: center;
}

/* 历史记录对话框 */
.records-content {
  padding: 10px 0;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.records-pagination {
  margin-top: 15px;
  text-align: center;
}

.no-records {
  text-align: center;
  padding: 40px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wheel-types-grid {
    grid-template-columns: 1fr;
  }

  .wheel-wrapper {
    width: 300px;
    height: 300px;
  }

  .wheel-text {
    font-size: 12px;
    max-width: 60px;
  }

  .wheel-game {
    padding: 15px;
  }

  .game-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .header-right {
    align-self: stretch;
    display: flex;
    gap: 10px;
  }

  .header-right .el-button {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .wheel-wrapper {
    width: 250px;
    height: 250px;
  }

  .wheel-text {
    font-size: 10px;
    max-width: 50px;
  }

  .wheel-type-card {
    padding: 20px 15px;
  }

  .wheel-type-icon {
    font-size: 36px;
  }
}
</style>
