<template>
  <div class="home-container">
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎来到个人空间</h1>
        <p class="welcome-subtitle">这里是您的专属数字世界</p>
        <div class="welcome-stats">
          <div class="stat-item">
            <el-icon class="stat-icon"><User /></el-icon>
            <div class="stat-info">
              <div class="stat-number">1</div>
              <div class="stat-label">用户</div>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon"><Cpu /></el-icon>
            <div class="stat-info">
              <div class="stat-number">0</div>
              <div class="stat-label">AI应用</div>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon"><VideoPlay /></el-icon>
            <div class="stat-info">
              <div class="stat-number">0</div>
              <div class="stat-label">小游戏</div>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon"><Document /></el-icon>
            <div class="stat-info">
              <div class="stat-number">0</div>
              <div class="stat-label">程序</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="features-section">
      <h2 class="section-title">功能模块</h2>
      <div class="features-grid">
        <div class="feature-card" @click="$router.push('/ai-app')">
          <el-icon class="feature-icon"><Cpu /></el-icon>
          <h3>AI应用</h3>
          <p>智能应用工具集合</p>
          <span class="coming-soon">即将上线</span>
        </div>
        <div class="feature-card" @click="$router.push('/games')">
          <el-icon class="feature-icon"><VideoPlay /></el-icon>
          <h3>小游戏</h3>
          <p>休闲娱乐游戏中心</p>
          <span class="coming-soon">即将上线</span>
        </div>
        <div class="feature-card" @click="$router.push('/programs')">
          <el-icon class="feature-icon"><Document /></el-icon>
          <h3>程序</h3>
          <p>实用程序工具箱</p>
          <span class="coming-soon">即将上线</span>
        </div>
        <div class="feature-card active" @click="$router.push('/profile')">
          <el-icon class="feature-icon"><User /></el-icon>
          <h3>个人中心</h3>
          <p>管理个人信息设置</p>
          <span class="available">可用</span>
        </div>
      </div>
    </div>

    <div class="info-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><InfoFilled /></el-icon>
                <span>系统信息</span>
              </div>
            </template>
            <div class="info-item">
              <span class="info-label">版本：</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">技术栈：</span>
              <span class="info-value">Spring Boot + Vue 3</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库：</span>
              <span class="info-value">MySQL</span>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Clock /></el-icon>
                <span>最近活动</span>
              </div>
            </template>
            <div class="activity-item">
              <div class="activity-time">{{ currentTime }}</div>
              <div class="activity-desc">当前时间</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Star /></el-icon>
                <span>快速操作</span>
              </div>
            </template>
            <el-button type="primary" @click="$router.push('/profile')" style="width: 100%; margin-bottom: 10px;">
              个人设置
            </el-button>
            <el-button @click="handleRefresh" style="width: 100%;">
              刷新页面
            </el-button>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { User, Cpu, VideoPlay, Document, InfoFilled, Clock, Star } from '@element-plus/icons-vue'

const currentTime = ref('')

// 更新当前时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN')
}

// 刷新页面
const handleRefresh = () => {
  location.reload()
}

let timer = null

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 60px 40px;
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.welcome-title {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 10px;
}

.welcome-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 40px;
}

.welcome-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 24px;
  opacity: 0.8;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.features-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #303133;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.feature-card.active {
  border: 2px solid #409eff;
}

.feature-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.feature-card h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
}

.feature-card p {
  color: #606266;
  font-size: 14px;
  margin-bottom: 16px;
}

.coming-soon {
  background: #f56c6c;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
}

.available {
  background: #67c23a;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
}

.info-section {
  margin-bottom: 30px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-label {
  color: #909399;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.activity-item {
  text-align: center;
}

.activity-time {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.activity-desc {
  color: #909399;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    padding: 40px 20px;
  }
  
  .welcome-title {
    font-size: 28px;
  }
  
  .welcome-subtitle {
    font-size: 16px;
  }
  
  .welcome-stats {
    gap: 20px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .welcome-section {
    padding: 30px 15px;
  }
  
  .welcome-title {
    font-size: 24px;
  }
  
  .welcome-stats {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
