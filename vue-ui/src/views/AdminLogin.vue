<template>
  <div class="admin-login-container">
    <!-- 动态星空背景 -->
    <div class="starry-background">
      <div class="galaxy"></div>
      <div class="stars"></div>
      <div class="stars2"></div>
      <div class="stars3"></div>
      <div class="shooting-star"></div>
      <div class="shooting-star shooting-star2"></div>
    </div>

    <div class="login-box">
      <div class="login-header">
        <h2>🛡️ 管理员登录</h2>
        <p>智玩空间管理后台</p>
      </div>

      <el-form :model="loginForm" :rules="rules" ref="loginFormRef" @submit.prevent="handleLogin">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入管理员账号"
            prefix-icon="User"
            size="large"
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            size="large"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <el-link @click="$router.push('/')">← 返回用户端</el-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage } from 'element-plus'

const router = useRouter()
const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入管理员账号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const response = await axios.post('/api/admin/login', loginForm)

    if (response.data.code === 200) {
      // 保存管理员登录状态
      sessionStorage.setItem('isAdminLoggedIn', 'true')
      ElMessage.success('登录成功')
      // 跳转到管理后台首页
      router.push('/admin/dashboard')
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error.response) {
      ElMessage.error(error.response.data.message || '登录失败')
    } else {
      ElMessage.error('网络错误，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 复用用户登录页面的星空背景样式 */
.starry-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
}

.galaxy {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  animation: galaxyMove 60s ease-in-out infinite alternate;
}

@keyframes galaxyMove {
  0% { transform: translateX(-20px) translateY(-10px) rotate(0deg); }
  100% { transform: translateX(20px) translateY(10px) rotate(1deg); }
}

.stars, .stars2, .stars3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: repeat;
}

.stars {
  background-image:
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent);
  background-size: 200px 100px;
  animation: sparkle 3s linear infinite;
}

.stars2 {
  background-image:
    radial-gradient(1px 1px at 25px 25px, rgba(255,255,255,0.7), transparent),
    radial-gradient(1px 1px at 50px 75px, #fff, transparent);
  background-size: 150px 150px;
  animation: sparkle 2s linear infinite reverse;
}

.stars3 {
  background-image:
    radial-gradient(1px 1px at 10px 10px, rgba(255,255,255,0.4), transparent),
    radial-gradient(1px 1px at 30px 60px, rgba(255,255,255,0.6), transparent);
  background-size: 100px 100px;
  animation: sparkle 4s linear infinite;
}

@keyframes sparkle {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

.shooting-star {
  position: absolute;
  top: 20%;
  left: 0;
  width: 2px;
  height: 2px;
  background: linear-gradient(45deg, #fff, transparent);
  border-radius: 50%;
  box-shadow: 0 0 6px 2px rgba(255, 255, 255, 0.8);
  animation: shootingStar 3s linear infinite;
}

.shooting-star2 {
  top: 60%;
  animation-delay: 1.5s;
  animation-duration: 4s;
}

@keyframes shootingStar {
  0% {
    transform: translateX(-100px) translateY(0px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 100px)) translateY(-200px);
    opacity: 0;
  }
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  padding: 40px;
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}

@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
    margin: 20px;
  }

  .login-header h2 {
    font-size: 20px;
  }
}
</style>
