<template>
  <div class="login-container">
    <!-- 动态星空背景 -->
    <div class="starry-background">
      <!-- 银河系背景 -->
      <div class="galaxy"></div>
      <!-- 闪烁星星 -->
      <div class="stars"></div>
      <div class="stars2"></div>
      <div class="stars3"></div>
      <!-- 流星 -->
      <div class="shooting-star"></div>
      <div class="shooting-star shooting-star2"></div>
    </div>

    <div class="login-box">
      <div class="login-header">
        <h2>欢迎登录</h2>
        <p>个人空间管理系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            show-password
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-btn"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login } from '../api/user'

const router = useRouter()

const loginFormRef = ref()
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    const res = await login(loginForm)
    
    // 登录成功，保存登录状态
    sessionStorage.setItem('isLoggedIn', 'true')
    ElMessage.success('登录成功')
    
    // 跳转到首页
    router.push('/')
    
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 动态星空背景 */
.starry-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
}

/* 银河系背景 */
.galaxy {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  animation: galaxyMove 60s ease-in-out infinite alternate;
}

@keyframes galaxyMove {
  0% { transform: translateX(-20px) translateY(-10px) rotate(0deg); }
  100% { transform: translateX(20px) translateY(10px) rotate(1deg); }
}

/* 星星层1 - 大星星 */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 3s linear infinite;
}

/* 星星层2 - 中等星星 */
.stars2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(1px 1px at 25px 25px, rgba(255,255,255,0.7), transparent),
    radial-gradient(1px 1px at 50px 75px, #fff, transparent),
    radial-gradient(1px 1px at 75px 25px, rgba(255,255,255,0.5), transparent),
    radial-gradient(1px 1px at 100px 75px, rgba(255,255,255,0.8), transparent);
  background-repeat: repeat;
  background-size: 150px 150px;
  animation: sparkle 2s linear infinite reverse;
}

/* 星星层3 - 小星星 */
.stars3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(1px 1px at 10px 10px, rgba(255,255,255,0.4), transparent),
    radial-gradient(1px 1px at 30px 60px, rgba(255,255,255,0.6), transparent),
    radial-gradient(1px 1px at 60px 10px, rgba(255,255,255,0.3), transparent),
    radial-gradient(1px 1px at 80px 60px, rgba(255,255,255,0.5), transparent);
  background-repeat: repeat;
  background-size: 100px 100px;
  animation: sparkle 4s linear infinite;
}

@keyframes sparkle {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

/* 流星效果 */
.shooting-star {
  position: absolute;
  top: 20%;
  left: 0;
  width: 2px;
  height: 2px;
  background: linear-gradient(45deg, #fff, transparent);
  border-radius: 50%;
  box-shadow: 0 0 6px 2px rgba(255, 255, 255, 0.8);
  animation: shootingStar 3s linear infinite;
}

.shooting-star2 {
  top: 60%;
  animation-delay: 1.5s;
  animation-duration: 4s;
}

@keyframes shootingStar {
  0% {
    transform: translateX(-100px) translateY(0px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 100px)) translateY(-200px);
    opacity: 0;
  }
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  padding: 40px;
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #303133;
  margin-bottom: 8px;
  font-weight: 600;
}

.login-header p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-top: 20px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-box {
    padding: 30px 20px;
  }
  
  .login-header h2 {
    font-size: 20px;
  }
}
</style>
