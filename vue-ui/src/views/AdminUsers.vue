<template>
  <div class="admin-users">
    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-form">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索用户名或昵称"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-select
          v-model="searchForm.status"
          placeholder="用户状态"
          style="width: 150px"
          clearable
        >
          <el-option label="全部" :value="null" />
          <el-option label="正常" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>

        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>

        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-table
        :data="userList"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable />

        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              {{ row.nickname?.charAt(0) || row.username?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column prop="username" label="用户名" min-width="120" />

        <el-table-column prop="nickname" label="昵称" min-width="120" />

        <el-table-column prop="email" label="邮箱" min-width="180" />

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'">
              {{ row.status ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="lastLoginTime" label="最后登录" width="180">
          <template #default="{ row }">
            {{ formatTime(row.lastLoginTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="createdTime" label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createdTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              :type="row.status ? 'danger' : 'success'"
              size="small"
              @click="handleStatusChange(row)"
            >
              {{ row.status ? '禁用' : '启用' }}
            </el-button>

            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="用户详情"
      width="600px"
    >
      <div class="user-detail" v-if="selectedUser">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedUser.avatar">
            {{ selectedUser.nickname?.charAt(0) || selectedUser.username?.charAt(0) }}
          </el-avatar>
          <div class="user-info">
            <h3>{{ selectedUser.nickname || selectedUser.username }}</h3>
            <p>ID: {{ selectedUser.id }}</p>
            <el-tag :type="selectedUser.status ? 'success' : 'danger'">
              {{ selectedUser.status ? '正常' : '禁用' }}
            </el-tag>
          </div>
        </div>

        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">
              {{ selectedUser.username }}
            </el-descriptions-item>
            <el-descriptions-item label="昵称">
              {{ selectedUser.nickname || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ selectedUser.email || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="性别">
              {{ getGenderText(selectedUser.gender) }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatTime(selectedUser.createdTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后登录">
              {{ formatTime(selectedUser.lastLoginTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button
          :type="selectedUser?.status ? 'danger' : 'success'"
          @click="handleStatusChange(selectedUser)"
        >
          {{ selectedUser?.status ? '禁用用户' : '启用用户' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

const loading = ref(false)
const userList = ref([])
const selectedUser = ref(null)
const showDetailDialog = ref(false)

const searchForm = reactive({
  keyword: '',
  status: null
})

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    const response = await axios.get('/api/admin/users', { params })

    if (response.data.code === 200) {
      const data = response.data.data
      userList.value = data.users
      pagination.total = data.total
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getUserList()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = null
  pagination.page = 1
  getUserList()
}

// 分页变化
const handlePageChange = () => {
  getUserList()
}

const handleSizeChange = () => {
  pagination.page = 1
  getUserList()
}

// 排序变化
const handleSortChange = ({ prop, order }) => {
  // 这里可以实现排序逻辑
  console.log('排序:', prop, order)
}

// 状态变更
const handleStatusChange = async (user) => {
  const action = user.status ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.nickname || user.username}" 吗？`,
      `${action}用户`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await axios.put(`/api/admin/users/${user.id}/status`, {
      status: !user.status
    })

    if (response.data.code === 200) {
      ElMessage.success(`${action}成功`)
      user.status = !user.status
      if (selectedUser.value && selectedUser.value.id === user.id) {
        selectedUser.value.status = user.status
      }
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态变更失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 查看详情
const handleViewDetail = (user) => {
  selectedUser.value = { ...user }
  showDetailDialog.value = true
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

// 获取性别文本
const getGenderText = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'other': '其他'
  }
  return genderMap[gender] || '-'
}

onMounted(() => {
  getUserList()
})
</script>

<style scoped>
.admin-users {
  max-width: 1200px;
  margin: 0 auto;
}

/* 搜索区域 */
.search-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

/* 表格区域 */
.table-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 用户详情 */
.user-detail {
  padding: 10px 0;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.user-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.user-info p {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.detail-content {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }

  .detail-header {
    flex-direction: column;
    text-align: center;
  }

  .user-info {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .search-section,
  .table-section {
    padding: 15px;
    margin: 0 -5px 15px -5px;
    border-radius: 8px;
  }

  .admin-users {
    margin: 0;
    padding: 0 5px;
  }
}
</style>
