<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="admin-sidebar">
        <div class="sidebar-header">
          <h3>🛡️ 管理后台</h3>
        </div>

        <el-menu
          :default-active="activeMenu"
          class="admin-menu"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409eff"
        >
          <el-menu-item index="/admin/dashboard">
            <el-icon><DataBoard /></el-icon>
            <span>数据概览</span>
          </el-menu-item>

          <el-menu-item index="/admin/users">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>

          <el-sub-menu index="content">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>内容管理</span>
            </template>
            <el-menu-item index="/admin/ai-roles">AI角色管理</el-menu-item>
            <el-menu-item index="/admin/roleplay-characters">角色扮演管理</el-menu-item>
            <el-menu-item index="/admin/scenarios">场景模板管理</el-menu-item>
          </el-sub-menu>

          <el-menu-item index="/admin/games">
            <el-icon><Trophy /></el-icon>
            <span>游戏管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="admin-header">
          <div class="header-left">
            <span class="page-title">{{ pageTitle }}</span>
          </div>

          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="admin-info">
                <el-avatar :size="32" :src="adminInfo.avatar">
                  {{ adminInfo.name?.charAt(0) }}
                </el-avatar>
                <span class="admin-name">{{ adminInfo.name }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 内容区域 -->
        <el-main class="admin-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DataBoard,
  User,
  Document,
  Trophy,
  ArrowDown
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const adminInfo = ref({
  name: '管理员',
  avatar: ''
})

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 页面标题映射
const pageTitleMap = {
  '/admin/dashboard': '数据概览',
  '/admin/users': '用户管理',
  '/admin/ai-roles': 'AI角色管理',
  '/admin/roleplay-characters': '角色扮演管理',
  '/admin/scenarios': '场景模板管理',
  '/admin/games': '游戏管理'
}

// 当前页面标题
const pageTitle = computed(() => {
  return pageTitleMap[route.path] || '管理后台'
})

// 获取管理员信息
const getAdminInfo = async () => {
  try {
    const response = await axios.get('/api/admin/info')
    if (response.data.code === 200) {
      adminInfo.value = response.data.data
    }
  } catch (error) {
    console.error('获取管理员信息失败:', error)
  }
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人设置功能开发中...')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await axios.post('/api/admin/logout')
    // 清除管理员登录状态
    sessionStorage.removeItem('isAdminLoggedIn')
    ElMessage.success('退出成功')
    router.push('/admin/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
    }
  }
}

onMounted(() => {
  getAdminInfo()
})
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.admin-sidebar {
  background-color: #304156;
  overflow: hidden;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #263445;
  border-bottom: 1px solid #1f2d3d;
}

.sidebar-header h3 {
  color: #fff;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.admin-menu {
  border: none;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.admin-menu:not(.el-menu--collapse) {
  width: 250px;
}

.admin-header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.header-left .page-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.admin-info:hover {
  background-color: #f5f7fa;
}

.admin-name {
  color: #303133;
  font-size: 14px;
}

.admin-main {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 200px !important;
  }

  .admin-menu:not(.el-menu--collapse) {
    width: 200px;
  }

  .admin-header {
    padding: 0 15px;
  }

  .header-left .page-title {
    font-size: 16px;
  }

  .admin-main {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .admin-sidebar {
    width: 180px !important;
  }

  .admin-menu:not(.el-menu--collapse) {
    width: 180px;
  }

  .sidebar-header h3 {
    font-size: 16px;
  }

  .admin-name {
    display: none;
  }
}
</style>
