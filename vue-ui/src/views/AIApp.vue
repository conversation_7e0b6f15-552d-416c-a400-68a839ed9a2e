<template>
  <div class="ai-app-container">
    <!-- 功能模块选择界面 -->
    <div v-if="currentView === 'home'" class="function-modules">
      <div class="modules-header">
        <h2>🤖 AI应用中心</h2>
        <p>选择您需要的AI功能，开始智能对话体验</p>
      </div>

      <div class="modules-grid">
        <!-- 基础聊天模块 -->
        <div class="module-card" @click="enterModule('chat')">
          <div class="module-icon">💬</div>
          <h3>基础聊天</h3>
          <p>智能AI助手，回答问题、协助思考</p>
          <div class="module-status">立即体验</div>
        </div>

        <!-- 哄哄模拟器模块 -->
        <div class="module-card" @click="enterModule('comfort')">
          <div class="module-icon">🤗</div>
          <h3>哄哄模拟器</h3>
          <p>情感安慰专家，温暖陪伴心灵</p>
          <div class="module-status">立即体验</div>
        </div>

        <!-- 角色扮演模块 -->
        <div class="module-card" @click="enterModule('roleplay')">
          <div class="module-icon">🎭</div>
          <h3>角色扮演</h3>
          <p>个性化AI角色，有趣互动体验</p>
          <div class="module-status">立即体验</div>
        </div>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div class="chat-container" v-else-if="currentView === 'chat'">
      <!-- 返回按钮 -->
      <div class="module-header">
        <el-button @click="backToHome" type="text" class="back-button">
          ← 返回功能选择
        </el-button>
        <h3>💬 基础聊天</h3>
      </div>
      <div class="chat-content">
        <!-- 会话侧栏 -->
        <div class="session-sidebar">
        <div class="sidebar-header">
          <el-button type="primary" @click="createNewSession" size="small">
            ➕ 新对话
          </el-button>
        </div>
        <div class="session-list">
          <div
            v-for="session in sessions"
            :key="session.id"
            :class="['session-item', { active: currentSessionId === session.id }]"
            @click="selectSession(session.id)"
          >
            <div class="session-name">{{ session.sessionName }}</div>
            <div class="session-actions">
              <el-button size="small" text @click.stop="renameSession(session)">✏️</el-button>
              <el-button size="small" text @click.stop="deleteSession(session.id)">🗑️</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天主区域 -->
      <div class="chat-main">
        <div class="chat-header">
          <h3>{{ getCurrentSessionName() }}</h3>
          <div class="header-actions">
            <el-button size="small" text @click="showAvatarSettings = true">
              🎨 头像设置
            </el-button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="message-list" ref="messageListRef">
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message-item', message.role]"
          >
            <div class="message-wrapper">
              <!-- 头像 -->
              <div class="message-avatar">
                <img
                  :src="message.role === 'user' ? userAvatar : aiAvatar"
                  :alt="message.role === 'user' ? '用户头像' : 'AI头像'"
                  @error="handleAvatarError"
                />
              </div>

              <!-- 消息内容 -->
              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-actions">
                  <el-button size="small" text @click="copyMessage(message.content)">📋</el-button>
                  <el-button
                    size="small"
                    text
                    @click="toggleFavorite(message)"
                    :class="{ favorited: message.isFavorite }"
                  >
                    {{ message.isFavorite ? '⭐' : '☆' }}
                  </el-button>
                </div>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.createdTime) }}</div>
          </div>

          <!-- 加载中提示 -->
          <div v-if="isLoading" class="message-item assistant">
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              placeholder="输入您的消息..."
              @keydown.ctrl.enter="sendMessage"
              :disabled="isLoading"
            />
            <div class="input-actions">
              <el-button
                type="primary"
                @click="sendMessage"
                :loading="isLoading"
                :disabled="!inputMessage.trim()"
              >
                发送 (Ctrl+Enter)
              </el-button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>

    <!-- 哄哄模拟器界面 -->
    <div class="comfort-container" v-else-if="currentView === 'comfort'">
      <!-- 返回按钮 -->
      <div class="module-header" v-if="comfortStage === 'scenario'">
        <el-button @click="backToHome" type="text" class="back-button">
          ← 返回功能选择
        </el-button>
        <h3>🤗 哄哄模拟器</h3>
      </div>
      <!-- 场景选择阶段 -->
      <div v-if="comfortStage === 'scenario'" class="comfort-scenario-selection">
        <div class="comfort-header">
          <h2>🤗 哄哄模拟器</h2>
          <p>选择一个适合你当前心情的场景，我会用心陪伴和安慰你</p>
        </div>

        <!-- 心情快速选择 -->
        <div class="mood-quick-select">
          <h3>你现在的心情是？</h3>
          <div class="mood-buttons">
            <el-button
              v-for="mood in moodOptions"
              :key="mood.type"
              :type="selectedMood === mood.type ? 'primary' : ''"
              @click="selectMood(mood.type)"
              class="mood-button"
            >
              {{ mood.emoji }} {{ mood.name }}
            </el-button>
          </div>
        </div>

        <!-- 场景列表 -->
        <div class="scenario-list">
          <h3>选择安慰场景</h3>
          <div class="scenario-grid">
            <div
              v-for="scenario in filteredScenarios"
              :key="scenario.id"
              :class="['scenario-card', { selected: selectedScenario?.id === scenario.id }]"
              @click="selectScenario(scenario)"
            >
              <div class="scenario-icon">{{ getScenarioIcon(scenario.scenarioType) }}</div>
              <h4>{{ scenario.scenarioName }}</h4>
              <p>{{ scenario.scenarioType === 'work' ? '工作压力' :
                   scenario.scenarioType === 'love' ? '感情问题' :
                   scenario.scenarioType === 'study' ? '学习焦虑' :
                   scenario.scenarioType === 'family' ? '家庭矛盾' : '身心健康' }}</p>
              <div class="scenario-usage">已帮助 {{ scenario.usageCount }} 人</div>
            </div>
          </div>
        </div>

        <!-- 开始按钮 -->
        <div class="start-comfort" v-if="selectedScenario && selectedMood">
          <el-button type="primary" size="large" @click="startComfortSession">
            开始安慰会话
          </el-button>
        </div>
      </div>

      <!-- 安慰聊天阶段 -->
      <div v-else-if="comfortStage === 'chat'" class="comfort-chat">
        <div class="comfort-chat-header">
          <div class="scenario-info">
            <span class="scenario-icon">{{ getScenarioIcon(currentComfortScenario?.scenarioType) }}</span>
            <span class="scenario-name">{{ currentComfortScenario?.scenarioName }}</span>
          </div>
          <div class="header-actions">
            <el-button size="small" @click="showMoodRecord = true">📊 心情记录</el-button>
            <el-button size="small" @click="backToScenarioSelection">🔙 重新选择</el-button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="comfort-message-list" ref="comfortMessageListRef">
          <div
            v-for="message in comfortMessages"
            :key="message.id"
            :class="['message-item', message.role]"
          >
            <div class="message-wrapper">
              <!-- 头像 -->
              <div class="message-avatar">
                <img
                  :src="message.role === 'user' ? userAvatar : aiAvatar"
                  :alt="message.role === 'user' ? '用户头像' : 'AI头像'"
                  @error="handleAvatarError"
                />
              </div>

              <!-- 消息内容 -->
              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-actions">
                  <el-button size="small" text @click="copyMessage(message.content)">📋</el-button>
                  <el-button
                    size="small"
                    text
                    @click="giveFeedback(message, 'helpful')"
                    :class="{ active: message.feedback === 'helpful' }"
                  >
                    👍 有帮助
                  </el-button>
                </div>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.createdTime) }}</div>
          </div>

          <!-- 加载中提示 -->
          <div v-if="comfortLoading" class="message-item assistant">
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="comfort-input-area">
          <div class="input-container">
            <el-input
              v-model="comfortInputMessage"
              type="textarea"
              :rows="3"
              placeholder="告诉我你的感受..."
              @keydown.ctrl.enter="sendComfortMessage"
              :disabled="comfortLoading"
            />
            <div class="input-actions">
              <el-button
                type="primary"
                @click="sendComfortMessage"
                :loading="comfortLoading"
                :disabled="!comfortInputMessage.trim()"
              >
                发送 (Ctrl+Enter)
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 角色扮演界面 -->
    <div class="roleplay-container" v-else-if="currentView === 'roleplay'">
      <!-- 返回按钮 -->
      <div class="module-header" v-if="roleplayStage === 'character'">
        <el-button @click="backToHome" type="text" class="back-button">
          ← 返回功能选择
        </el-button>
        <h3>🎭 角色扮演</h3>
      </div>
      <!-- 角色选择阶段 -->
      <div v-if="roleplayStage === 'character'" class="roleplay-character-selection">
        <div class="roleplay-header">
          <h2>🎭 角色扮演</h2>
          <p>选择一个你喜欢的角色，开始一段有趣的对话</p>
        </div>

        <!-- 筛选和搜索 -->
        <div class="character-filters">
          <div class="filter-row">
            <el-select v-model="selectedCharacterType" placeholder="选择类型" clearable @change="filterCharacters">
              <el-option label="全部类型" value=""></el-option>
              <el-option v-for="type in characterTypes" :key="type" :label="getTypeLabel(type)" :value="type"></el-option>
            </el-select>

            <el-select v-model="selectedCategory" placeholder="选择分类" clearable @change="filterCharacters">
              <el-option label="全部分类" value=""></el-option>
              <el-option v-for="category in categories" :key="category" :label="category" :value="category"></el-option>
            </el-select>

            <el-input
              v-model="characterSearchKeyword"
              placeholder="搜索角色名称..."
              @input="searchCharacters"
              clearable
            >
              <template #prefix>
                <el-icon>🔍</el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 角色列表 -->
        <div class="character-list">
          <div class="character-grid">
            <div
              v-for="character in filteredCharacters"
              :key="character.id"
              :class="['character-card', { selected: selectedCharacter?.id === character.id }]"
              @click="selectCharacter(character)"
            >
              <div class="character-avatar">
                <img :src="character.avatarUrl || getDefaultAvatar(character.category)" :alt="character.characterName" />
              </div>
              <div class="character-info">
                <h4>{{ character.characterName }}</h4>
                <p class="character-category">{{ character.category }}</p>
                <p class="character-description">{{ character.description }}</p>
                <div class="character-tags">
                  <span v-for="tag in getCharacterTags(character.tags)" :key="tag" class="tag">{{ tag }}</span>
                </div>
                <div class="character-stats">
                  <span class="usage-count">💬 {{ character.usageCount }}</span>
                  <span class="rating">⭐ {{ character.rating || '0.0' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 开始按钮 -->
        <div class="start-roleplay" v-if="selectedCharacter">
          <el-button type="primary" size="large" @click="startRoleplaySession">
            开始与{{ selectedCharacter.characterName }}对话
          </el-button>
        </div>
      </div>

      <!-- 角色扮演聊天阶段 -->
      <div v-else-if="roleplayStage === 'chat'" class="roleplay-chat">
        <div class="roleplay-chat-header">
          <div class="character-info">
            <img :src="currentRoleplayCharacter?.avatarUrl || getDefaultAvatar(currentRoleplayCharacter?.category)"
                 :alt="currentRoleplayCharacter?.characterName" class="character-avatar-small" />
            <div class="character-details">
              <span class="character-name">{{ currentRoleplayCharacter?.characterName }}</span>
              <span class="character-category">{{ currentRoleplayCharacter?.category }}</span>
            </div>
          </div>
          <div class="header-actions">
            <el-button size="small" @click="showCharacterDetail = true">👤 角色详情</el-button>
            <el-button size="small" @click="backToCharacterSelection">🔙 重新选择</el-button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="roleplay-message-list" ref="roleplayMessageListRef">
          <div
            v-for="message in roleplayMessages"
            :key="message.id"
            :class="['message-item', message.role]"
          >
            <div class="message-wrapper">
              <!-- 头像 -->
              <div class="message-avatar">
                <img
                  :src="message.role === 'user' ? userAvatar : (currentRoleplayCharacter?.avatarUrl || getDefaultAvatar(currentRoleplayCharacter?.category))"
                  :alt="message.role === 'user' ? '用户头像' : currentRoleplayCharacter?.characterName"
                  @error="handleAvatarError"
                />
              </div>

              <!-- 消息内容 -->
              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-actions">
                  <el-button size="small" text @click="copyMessage(message.content)">📋</el-button>
                  <el-button
                    size="small"
                    text
                    @click="toggleRoleplayMessageFavorite(message)"
                    :class="{ favorited: message.isFavorite }"
                  >
                    {{ message.isFavorite ? '⭐' : '☆' }}
                  </el-button>
                </div>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.createdTime) }}</div>
          </div>

          <!-- 加载中提示 -->
          <div v-if="roleplayLoading" class="message-item character">
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="roleplay-input-area">
          <div class="input-container">
            <el-input
              v-model="roleplayInputMessage"
              type="textarea"
              :rows="3"
              placeholder="输入你想说的话..."
              @keydown.ctrl.enter="sendRoleplayMessage"
              :disabled="roleplayLoading"
            />
            <div class="input-actions">
              <el-button
                type="primary"
                @click="sendRoleplayMessage"
                :loading="roleplayLoading"
                :disabled="!roleplayInputMessage.trim()"
              >
                发送 (Ctrl+Enter)
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 其他功能的占位符 -->
    <div v-else class="placeholder-container">
      <div class="placeholder-content">
        <div class="placeholder-icon">🚧</div>
        <h2>功能开发中</h2>
        <p>该功能正在开发中，敬请期待！</p>
      </div>
    </div>

    <!-- 头像设置对话框 -->
    <el-dialog v-model="showAvatarSettings" title="头像设置" width="600px">
      <el-tabs v-model="avatarTab">
        <!-- 用户头像设置 -->
        <el-tab-pane label="我的头像" name="user">
          <div class="avatar-setting-section">
            <div class="current-avatar">
              <el-avatar :src="userAvatar" :size="80">
                👤
              </el-avatar>
              <p>当前头像</p>
            </div>
            <div class="avatar-upload">
              <el-upload
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :http-request="handleUserAvatarUpload"
                accept="image/*"
              >
                <el-button type="primary" :loading="userAvatarUploading">
                  {{ userAvatarUploading ? '上传中...' : '更换头像' }}
                </el-button>
              </el-upload>
              <p class="upload-tip">支持 JPG、PNG 格式，文件大小不超过 5MB</p>
            </div>
          </div>
        </el-tab-pane>

        <!-- AI头像设置 -->
        <el-tab-pane label="AI头像" name="ai">
          <div class="avatar-setting-section">
            <div class="current-avatar">
              <el-avatar :src="aiAvatar" :size="80">
                <el-icon>🤖</el-icon>
              </el-avatar>
              <p>当前AI头像</p>
            </div>

            <!-- AI头像列表 -->
            <div class="ai-avatar-list">
              <div
                v-for="avatar in aiAvatarList"
                :key="avatar.id"
                :class="['ai-avatar-item', { active: avatar.isDefault }]"
                @click="setDefaultAiAvatar(avatar.id)"
              >
                <el-avatar :src="avatar.avatarUrl" :size="50" />
                <span>{{ avatar.avatarName }}</span>
                <el-button
                  size="small"
                  text
                  type="danger"
                  @click.stop="deleteAiAvatar(avatar.id)"
                  v-if="!avatar.isDefault"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 上传新AI头像 -->
            <div class="avatar-upload">
              <el-input
                v-model="newAiAvatarName"
                placeholder="请输入头像名称"
                style="margin-bottom: 10px;"
              />
              <el-upload
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :http-request="handleAiAvatarUpload"
                accept="image/*"
              >
                <el-button type="primary" :loading="aiAvatarUploading">
                  {{ aiAvatarUploading ? '上传中...' : '添加新头像' }}
                </el-button>
              </el-upload>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 角色详情对话框 -->
    <el-dialog v-model="showCharacterDetail" title="角色详情" width="600px">
      <div v-if="currentRoleplayCharacter" class="character-detail">
        <div class="character-detail-header">
          <img :src="currentRoleplayCharacter.avatarUrl || getDefaultAvatar(currentRoleplayCharacter.category)"
               :alt="currentRoleplayCharacter.characterName" class="character-detail-avatar" />
          <div class="character-detail-info">
            <h3>{{ currentRoleplayCharacter.characterName }}</h3>
            <p class="character-detail-category">{{ currentRoleplayCharacter.category }}</p>
            <div class="character-detail-stats">
              <span>💬 {{ currentRoleplayCharacter.usageCount }} 次对话</span>
              <span>⭐ {{ currentRoleplayCharacter.rating || '0.0' }} 分</span>
            </div>
          </div>
        </div>

        <div class="character-detail-content">
          <div class="detail-section">
            <h4>📝 角色描述</h4>
            <p>{{ currentRoleplayCharacter.description }}</p>
          </div>

          <div class="detail-section">
            <h4>🎭 性格特点</h4>
            <p>{{ currentRoleplayCharacter.personality }}</p>
          </div>

          <div class="detail-section" v-if="currentRoleplayCharacter.speakingStyle">
            <h4>💬 说话风格</h4>
            <p>{{ currentRoleplayCharacter.speakingStyle }}</p>
          </div>

          <div class="detail-section" v-if="currentRoleplayCharacter.backgroundStory">
            <h4>📚 背景故事</h4>
            <p>{{ currentRoleplayCharacter.backgroundStory }}</p>
          </div>

          <div class="detail-section" v-if="getCharacterTags(currentRoleplayCharacter.tags).length > 0">
            <h4>🏷️ 标签</h4>
            <div class="character-detail-tags">
              <span v-for="tag in getCharacterTags(currentRoleplayCharacter.tags)" :key="tag" class="detail-tag">{{ tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

// 响应式数据
const currentView = ref('home') // 'home' | 'chat' | 'comfort' | 'roleplay'
const sessions = ref([])
const currentSessionId = ref(null)
const messages = ref([])
const inputMessage = ref('')
const isLoading = ref(false)
const messageListRef = ref()

// 头像相关数据
const userAvatar = ref('https://api.dicebear.com/7.x/avataaars/svg?seed=user&backgroundColor=c0aede')
const aiAvatar = ref('https://api.dicebear.com/7.x/avataaars/svg?seed=ai&backgroundColor=d1d4f9')
const showAvatarSettings = ref(false)
const avatarTab = ref('user')
const userAvatarUploading = ref(false)
const aiAvatarUploading = ref(false)
const aiAvatarList = ref([])
const newAiAvatarName = ref('')

// 哄哄模拟器相关数据
const comfortStage = ref('scenario') // 'scenario' | 'chat'
const selectedMood = ref('')
const selectedScenario = ref(null)
const currentComfortScenario = ref(null)
const comfortMessages = ref([])
const comfortInputMessage = ref('')
const comfortLoading = ref(false)
const comfortMessageListRef = ref()
const showMoodRecord = ref(false)
const scenarios = ref([])

// 心情选项
const moodOptions = ref([
  { type: 'sad', name: '难过', emoji: '😢' },
  { type: 'angry', name: '生气', emoji: '😠' },
  { type: 'stressed', name: '压力大', emoji: '😰' },
  { type: 'lonely', name: '孤独', emoji: '😔' },
  { type: 'tired', name: '疲惫', emoji: '😴' }
])

// 角色扮演相关数据
const roleplayStage = ref('character') // 'character' | 'chat'
const characters = ref([])
const filteredCharacters = ref([])
const selectedCharacter = ref(null)
const currentRoleplayCharacter = ref(null)
const currentRoleplaySessionId = ref(null)
const roleplayMessages = ref([])
const roleplayInputMessage = ref('')
const roleplayLoading = ref(false)
const roleplayMessageListRef = ref()
const showCharacterDetail = ref(false)

// 角色筛选相关
const selectedCharacterType = ref('')
const selectedCategory = ref('')
const characterSearchKeyword = ref('')
const characterTypes = ref([])
const categories = ref([])

// 计算属性
const filteredScenarios = computed(() => {
  if (!selectedMood.value) return scenarios.value
  return scenarios.value.filter(scenario =>
    scenario.moodTypes.includes(selectedMood.value)
  )
})

// 生命周期
onMounted(() => {
  // 只加载头像数据，其他数据按需加载
  loadAvatars()
})

// 方法
const enterModule = (moduleName) => {
  currentView.value = moduleName
  console.log('进入模块:', moduleName)

  // 根据模块初始化相应数据
  if (moduleName === 'chat') {
    if (sessions.value.length === 0) {
      loadSessions()
    }
  } else if (moduleName === 'comfort') {
    comfortStage.value = 'scenario'
    if (scenarios.value.length === 0) {
      loadComfortScenarios()
    }
  } else if (moduleName === 'roleplay') {
    roleplayStage.value = 'character'
    if (characters.value.length === 0) {
      loadRoleplayCharacters()
    }
  }
}

const backToHome = () => {
  currentView.value = 'home'

  // 重置各模块状态
  comfortStage.value = 'scenario'
  roleplayStage.value = 'character'
  selectedMood.value = ''
  selectedScenario.value = null
  selectedCharacter.value = null
  currentComfortScenario.value = null
  currentRoleplayCharacter.value = null
  comfortMessages.value = []
  roleplayMessages.value = []
}

const loadSessions = async () => {
  try {
    const response = await axios.get('/api/ai/sessions')

    if (response.data.code === 200) {
      sessions.value = response.data.data

      if (sessions.value.length > 0) {
        await selectSession(sessions.value[0].id)
      } else {
        await createNewSession()
      }
    }
  } catch (error) {
    console.error('加载会话失败:', error)
    ElMessage.error('加载会话失败')
  }
}

const createNewSession = async () => {
  try {
    const response = await axios.post('/api/ai/sessions', {
      sessionName: '新对话',
      sessionType: 'chat'
    })
    if (response.data.code === 200) {
      const newSession = response.data.data
      sessions.value.unshift(newSession)
      await selectSession(newSession.id)

      // 添加AI欢迎消息
      messages.value = [{
        id: Date.now(),
        role: 'assistant',
        content: '你好！我是你的AI助手，很高兴为你服务！我可以帮你解答问题、进行对话、协助思考等。有什么我可以帮助你的吗？',
        createdTime: new Date().toISOString(),
        isFavorite: false
      }]

      ElMessage.success('创建新对话成功')
    }
  } catch (error) {
    console.error('创建会话失败:', error)
    ElMessage.error('创建会话失败')
  }
}

const selectSession = async (sessionId) => {
  currentSessionId.value = sessionId
  await loadMessages(sessionId)
}

const loadMessages = async (sessionId) => {
  try {
    const response = await axios.get(`/api/ai/sessions/${sessionId}/messages`)
    if (response.data.code === 200) {
      messages.value = response.data.data
      await nextTick()
      scrollToBottom()
    }
  } catch (error) {
    console.error('加载消息失败:', error)
    ElMessage.error('加载消息失败')
  }
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value || !currentSessionId.value) {
    return
  }

  const userMessage = inputMessage.value.trim()
  inputMessage.value = ''
  isLoading.value = true

  // 添加用户消息到界面
  messages.value.push({
    id: Date.now(),
    role: 'user',
    content: userMessage,
    createdTime: new Date().toISOString(),
    isFavorite: false
  })

  await nextTick()
  scrollToBottom()

  try {
    const response = await axios.post('/api/ai/chat/send', {
      sessionId: currentSessionId.value,
      message: userMessage
    })

    if (response.data.code === 200) {
      // 添加AI回复到界面
      messages.value.push({
        id: Date.now() + 1,
        role: 'assistant',
        content: response.data.data,
        createdTime: new Date().toISOString(),
        isFavorite: false
      })

      await nextTick()
      scrollToBottom()
    } else {
      ElMessage.error(response.data.message || '发送消息失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    isLoading.value = false
  }
}

const renameSession = async (session) => {
  try {
    const { value: newName } = await ElMessageBox.prompt('请输入新的对话名称', '重命名对话', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: session.sessionName
    })

    if (newName) {
      const response = await axios.put(`/api/ai/sessions/${session.id}/name`, {
        sessionName: newName
      })

      if (response.data.success) {
        session.sessionName = newName
        ElMessage.success('重命名成功')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重命名失败:', error)
      ElMessage.error('重命名失败')
    }
  }
}

const deleteSession = async (sessionId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个对话吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await axios.delete(`/api/ai/sessions/${sessionId}`)
    if (response.data.success) {
      sessions.value = sessions.value.filter(s => s.id !== sessionId)
      if (currentSessionId.value === sessionId) {
        if (sessions.value.length > 0) {
          selectSession(sessions.value[0].id)
        } else {
          currentSessionId.value = null
          messages.value = []
        }
      }
      ElMessage.success('删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const toggleFavorite = async (message) => {
  try {
    const response = await axios.put(`/api/ai/messages/${message.id}/favorite`, {
      isFavorite: !message.isFavorite
    })

    if (response.data.success) {
      message.isFavorite = !message.isFavorite
      ElMessage.success(message.isFavorite ? '已收藏' : '已取消收藏')
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const getCurrentSessionName = () => {
  const session = sessions.value.find(s => s.id === currentSessionId.value)
  return session ? session.sessionName : '选择对话'
}

const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 头像相关方法
const loadAvatars = async () => {
  try {
    // 加载用户头像
    const userResponse = await axios.get('/api/avatar/user/info')
    if (userResponse.data.code === 200) {
      userAvatar.value = userResponse.data.data.avatarUrl
    }

    // 加载默认AI头像
    const aiResponse = await axios.get('/api/avatar/ai/default')
    if (aiResponse.data.code === 200) {
      aiAvatar.value = aiResponse.data.data
    }

    // 加载AI头像列表
    await loadAiAvatarList()
  } catch (error) {
    console.error('加载头像失败:', error)
  }
}

const handleAvatarError = (event) => {
  // 头像加载失败时使用默认头像
  if (event.target.src.includes('user') || event.target.src.includes('default-user')) {
    event.target.src = 'https://api.dicebear.com/7.x/avataaars/svg?seed=user&backgroundColor=c0aede'
  } else {
    event.target.src = 'https://api.dicebear.com/7.x/avataaars/svg?seed=ai&backgroundColor=d1d4f9'
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 处理用户头像上传
const handleUserAvatarUpload = async ({ file }) => {
  try {
    userAvatarUploading.value = true
    const formData = new FormData()
    formData.append('file', file)

    const response = await axios.post('/api/avatar/user/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.data.code === 200) {
      userAvatar.value = response.data.data
      ElMessage.success('用户头像上传成功')
    } else {
      ElMessage.error(response.data.message || '头像上传失败')
    }
  } catch (error) {
    console.error('用户头像上传失败:', error)
    ElMessage.error('头像上传失败')
  } finally {
    userAvatarUploading.value = false
  }
}

// 处理AI头像上传
const handleAiAvatarUpload = async ({ file }) => {
  if (!newAiAvatarName.value.trim()) {
    ElMessage.error('请输入头像名称')
    return
  }

  try {
    aiAvatarUploading.value = true
    const formData = new FormData()
    formData.append('file', file)
    formData.append('avatarName', newAiAvatarName.value)

    const response = await axios.post('/api/avatar/ai/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.data.code === 200) {
      await loadAiAvatarList()
      newAiAvatarName.value = ''
      ElMessage.success('AI头像上传成功')
    } else {
      ElMessage.error(response.data.message || 'AI头像上传失败')
    }
  } catch (error) {
    console.error('AI头像上传失败:', error)
    ElMessage.error('AI头像上传失败')
  } finally {
    aiAvatarUploading.value = false
  }
}

// 加载AI头像列表
const loadAiAvatarList = async () => {
  try {
    const response = await axios.get('/api/avatar/ai/list')
    if (response.data.code === 200) {
      aiAvatarList.value = response.data.data
    }
  } catch (error) {
    console.error('加载AI头像列表失败:', error)
  }
}

// 设置默认AI头像
const setDefaultAiAvatar = async (avatarId) => {
  try {
    const response = await axios.put(`/api/avatar/ai/default/${avatarId}`)
    if (response.data.code === 200) {
      await loadAiAvatarList()
      await loadAvatars()
      ElMessage.success('设置默认头像成功')
    }
  } catch (error) {
    console.error('设置默认头像失败:', error)
    ElMessage.error('设置失败')
  }
}

// 删除AI头像
const deleteAiAvatar = async (avatarId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个头像吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await axios.delete(`/api/avatar/ai/${avatarId}`)
    if (response.data.code === 200) {
      await loadAiAvatarList()
      ElMessage.success('删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除头像失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 哄哄模拟器相关方法
const loadComfortScenarios = async () => {
  try {
    const response = await axios.get('/api/comfort/scenarios')
    if (response.data.code === 200) {
      scenarios.value = response.data.data
    }
  } catch (error) {
    console.error('加载安慰场景失败:', error)
  }
}

const selectMood = (moodType) => {
  selectedMood.value = moodType
  selectedScenario.value = null // 重置场景选择
}

const selectScenario = (scenario) => {
  selectedScenario.value = scenario
}

const getScenarioIcon = (scenarioType) => {
  const icons = {
    work: '💼',
    love: '💕',
    study: '📚',
    family: '👨‍👩‍👧‍👦',
    health: '🏥'
  }
  return icons[scenarioType] || '🤗'
}

const startComfortSession = async () => {
  try {
    const response = await axios.post('/api/comfort/start-session', {
      scenarioId: selectedScenario.value.id,
      moodType: selectedMood.value,
      moodLevel: 5 // 默认中等程度
    })

    if (response.data.code === 200) {
      currentComfortScenario.value = selectedScenario.value
      comfortStage.value = 'chat'

      // 添加AI的问候消息
      comfortMessages.value = [{
        id: Date.now(),
        role: 'assistant',
        content: response.data.data.greeting,
        createdTime: new Date().toISOString(),
        feedback: null
      }]

      await nextTick()
      scrollComfortToBottom()
    }
  } catch (error) {
    console.error('开始安慰会话失败:', error)
    ElMessage.error('开始会话失败')
  }
}

const sendComfortMessage = async () => {
  if (!comfortInputMessage.value.trim() || comfortLoading.value) {
    return
  }

  const userMessage = comfortInputMessage.value.trim()
  comfortInputMessage.value = ''
  comfortLoading.value = true

  // 添加用户消息到界面
  comfortMessages.value.push({
    id: Date.now(),
    role: 'user',
    content: userMessage,
    createdTime: new Date().toISOString(),
    feedback: null
  })

  await nextTick()
  scrollComfortToBottom()

  try {
    const response = await axios.post('/api/comfort/generate-reply', {
      scenarioId: currentComfortScenario.value.id,
      userMessage: userMessage,
      moodType: selectedMood.value
    })

    if (response.data.code === 200) {
      // 添加AI回复到界面
      comfortMessages.value.push({
        id: Date.now() + 1,
        role: 'assistant',
        content: response.data.data,
        createdTime: new Date().toISOString(),
        feedback: null
      })

      await nextTick()
      scrollComfortToBottom()
    } else {
      ElMessage.error(response.data.message || '发送消息失败')
    }
  } catch (error) {
    console.error('发送安慰消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    comfortLoading.value = false
  }
}

const backToScenarioSelection = () => {
  comfortStage.value = 'scenario'
  comfortMessages.value = []
  currentComfortScenario.value = null
}

const scrollComfortToBottom = () => {
  if (comfortMessageListRef.value) {
    comfortMessageListRef.value.scrollTop = comfortMessageListRef.value.scrollHeight
  }
}

const giveFeedback = async (message, feedbackType) => {
  try {
    message.feedback = feedbackType
    // 这里可以发送反馈到后端
    ElMessage.success('感谢你的反馈！')
  } catch (error) {
    console.error('反馈失败:', error)
  }
}

// 角色扮演相关方法
const loadRoleplayCharacters = async () => {
  try {
    const response = await axios.get('/api/roleplay/characters')
    if (response.data.code === 200) {
      characters.value = response.data.data
      filteredCharacters.value = response.data.data
    }

    // 加载筛选选项
    const filtersResponse = await axios.get('/api/roleplay/filters')
    if (filtersResponse.data.code === 200) {
      characterTypes.value = filtersResponse.data.data.types || []
      categories.value = filtersResponse.data.data.categories || []
    }
  } catch (error) {
    console.error('加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  }
}

const selectCharacter = (character) => {
  selectedCharacter.value = character
}

const filterCharacters = () => {
  let filtered = characters.value

  if (selectedCharacterType.value) {
    filtered = filtered.filter(char => char.characterType === selectedCharacterType.value)
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(char => char.category === selectedCategory.value)
  }

  if (characterSearchKeyword.value) {
    const keyword = characterSearchKeyword.value.toLowerCase()
    filtered = filtered.filter(char =>
      char.characterName.toLowerCase().includes(keyword) ||
      char.description.toLowerCase().includes(keyword) ||
      (char.tags && char.tags.toLowerCase().includes(keyword))
    )
  }

  filteredCharacters.value = filtered
}

const searchCharacters = () => {
  filterCharacters()
}

const getTypeLabel = (type) => {
  const typeLabels = {
    'anime': '动漫',
    'game': '游戏',
    'movie': '影视',
    'historical': '历史',
    'custom': '自定义'
  }
  return typeLabels[type] || type
}

const getDefaultAvatar = (category) => {
  const defaultAvatars = {
    '恋人': 'https://api.dicebear.com/7.x/avataaars/svg?seed=lover&backgroundColor=ffdfbf',
    '朋友': 'https://api.dicebear.com/7.x/avataaars/svg?seed=friend&backgroundColor=c0aede',
    '老师': 'https://api.dicebear.com/7.x/avataaars/svg?seed=teacher&backgroundColor=d1d4f9',
    '医生': 'https://api.dicebear.com/7.x/avataaars/svg?seed=doctor&backgroundColor=fecaca',
    '明星': 'https://api.dicebear.com/7.x/avataaars/svg?seed=star&backgroundColor=fed7aa'
  }
  return defaultAvatars[category] || 'https://api.dicebear.com/7.x/avataaars/svg?seed=default&backgroundColor=e5e7eb'
}

const getCharacterTags = (tags) => {
  if (!tags) return []
  return tags.split(',').map(tag => tag.trim()).slice(0, 3)
}

const startRoleplaySession = async () => {
  if (!selectedCharacter.value) return

  try {
    const response = await axios.post('/api/roleplay/start', {
      characterId: selectedCharacter.value.id
    })

    if (response.data.code === 200) {
      const result = response.data.data
      currentRoleplaySessionId.value = result.sessionId
      currentRoleplayCharacter.value = result.character
      roleplayStage.value = 'chat'

      // 添加角色的开场白
      roleplayMessages.value = [{
        id: Date.now(),
        role: 'character',
        content: result.greeting,
        createdTime: new Date().toISOString(),
        isFavorite: false
      }]

      await nextTick()
      scrollRoleplayToBottom()

      ElMessage.success(`开始与${selectedCharacter.value.characterName}的对话`)
    } else {
      ElMessage.error(response.data.message || '启动角色扮演失败')
    }
  } catch (error) {
    console.error('启动角色扮演失败:', error)
    ElMessage.error('启动角色扮演失败')
  }
}

const backToCharacterSelection = () => {
  roleplayStage.value = 'character'
  selectedCharacter.value = null
  currentRoleplayCharacter.value = null
  currentRoleplaySessionId.value = null
  roleplayMessages.value = []
}

const sendRoleplayMessage = async () => {
  if (!roleplayInputMessage.value.trim() || roleplayLoading.value || !currentRoleplaySessionId.value) {
    return
  }

  const userMessage = roleplayInputMessage.value.trim()
  roleplayInputMessage.value = ''
  roleplayLoading.value = true

  // 添加用户消息到界面
  roleplayMessages.value.push({
    id: Date.now(),
    role: 'user',
    content: userMessage,
    createdTime: new Date().toISOString(),
    isFavorite: false
  })

  await nextTick()
  scrollRoleplayToBottom()

  try {
    const response = await axios.post(`/api/roleplay/sessions/${currentRoleplaySessionId.value}/send`, {
      message: userMessage
    })

    if (response.data.code === 200) {
      // 添加角色回复到界面
      roleplayMessages.value.push({
        id: Date.now() + 1,
        role: 'character',
        content: response.data.data,
        createdTime: new Date().toISOString(),
        isFavorite: false
      })

      await nextTick()
      scrollRoleplayToBottom()
    } else {
      ElMessage.error(response.data.message || '发送消息失败')
    }
  } catch (error) {
    console.error('发送角色扮演消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    roleplayLoading.value = false
  }
}

const scrollRoleplayToBottom = () => {
  if (roleplayMessageListRef.value) {
    roleplayMessageListRef.value.scrollTop = roleplayMessageListRef.value.scrollHeight
  }
}

const toggleRoleplayMessageFavorite = async (message) => {
  try {
    const response = await axios.post(`/api/roleplay/messages/${message.id}/favorite`)
    if (response.data.code === 200) {
      message.isFavorite = !message.isFavorite
      ElMessage.success(message.isFavorite ? '已收藏' : '已取消收藏')
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    ElMessage.error('操作失败')
  }
}





// 生命周期
onMounted(() => {
  // 只加载头像数据，其他数据按需加载
  loadAvatars()
})

</script>

<style scoped>
.ai-app-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

/* 功能模块选择界面样式 */
.function-modules {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modules-header {
  text-align: center;
  margin-bottom: 50px;
}

.modules-header h2 {
  font-size: 32px;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.modules-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  max-width: 1000px;
  width: 100%;
}

.module-card {
  background: white;
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  color: #303133;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.module-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.module-card h3 {
  font-size: 20px;
  margin: 0 0 15px 0;
  color: #303133;
  font-weight: 600;
}

.module-card p {
  font-size: 14px;
  color: #606266;
  margin: 0 0 25px 0;
  line-height: 1.5;
}

.module-status {
  background: #409eff;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
}

/* 模块头部样式 */
.module-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  gap: 15px;
}

.back-button {
  color: #409eff;
  font-size: 14px;
  padding: 0;
}

.back-button:hover {
  color: #66b1ff;
}

.module-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
}

.chat-container .chat-content {
  flex: 1;
  display: flex;
}

/* 会话侧栏 */
.session-sidebar {
  width: 280px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.session-item:hover {
  background-color: #f5f7fa;
}

.session-item.active {
  background-color: #e6f7ff;
  border: 1px solid #409eff;
}

.session-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.session-item:hover .session-actions {
  opacity: 1;
}

/* 聊天主区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 消息列表 */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
}

.message-item {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.message-item.user {
  align-items: flex-end;
}

.message-item.assistant {
  align-items: flex-start;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 80%;
}

.message-item.user .message-wrapper {
  flex-direction: row-reverse;
}

/* 头像样式 */
.message-avatar {
  flex-shrink: 0;
}

.message-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e4e7ed;
  background-color: #f5f7fa;
}

.message-item.user .message-avatar img {
  border-color: #409eff;
}

.message-item.assistant .message-avatar img {
  border-color: #67c23a;
}

.message-content {
  flex: 1;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.message-text {
  padding: 12px 16px;
  border-radius: 12px;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-item.user .message-text {
  background-color: #409eff;
  color: white;
}

.message-item.assistant .message-text {
  background-color: white;
  color: #303133;
  border: 1px solid #e4e7ed;
}

.message-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-content:hover .message-actions {
  opacity: 1;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.message-item.user .message-time {
  text-align: right;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #409eff;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.input-area {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: white;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
}

/* 收藏状态 */
.favorited {
  color: #f56c6c !important;
}

/* 占位符样式 */
.placeholder-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.placeholder-content {
  max-width: 500px;
  padding: 40px;
}

.placeholder-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.placeholder-content h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 16px;
}

.placeholder-content p {
  color: #606266;
  font-size: 16px;
  line-height: 1.6;
}

/* 头像设置对话框样式 */
.avatar-setting-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.current-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.current-avatar p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-tip {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.ai-avatar-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.ai-avatar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-avatar-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.ai-avatar-item.active {
  border-color: #409eff;
  background-color: #e6f7ff;
}

.ai-avatar-item span {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }

  .session-sidebar {
    width: 100%;
    height: 200px;
  }

  .message-content {
    max-width: 90%;
  }

  .ai-avatar-list {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}

/* 哄哄模拟器样式 */
.comfort-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comfort-scenario-selection {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.comfort-header {
  text-align: center;
  margin-bottom: 30px;
}

.comfort-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.comfort-header p {
  color: #606266;
  font-size: 16px;
}

.mood-quick-select {
  margin-bottom: 30px;
}

.mood-quick-select h3 {
  margin-bottom: 15px;
  color: #303133;
}

.mood-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.mood-button {
  min-width: 120px;
  height: 50px;
  font-size: 16px;
  border-radius: 25px;
}

.scenario-list h3 {
  margin-bottom: 20px;
  color: #303133;
  text-align: center;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.scenario-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.scenario-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.scenario-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.scenario-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.scenario-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.scenario-card p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

.scenario-usage {
  color: #909399;
  font-size: 12px;
}

.start-comfort {
  text-align: center;
  margin-top: 30px;
}

/* 安慰聊天界面样式 */
.comfort-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comfort-chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scenario-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.scenario-info .scenario-icon {
  font-size: 24px;
}

.scenario-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.comfort-message-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
}

.comfort-input-area {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: white;
}

/* 消息反馈按钮样式 */
.message-actions .el-button.active {
  color: #409eff;
  background-color: #ecf5ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scenario-grid {
    grid-template-columns: 1fr;
  }

  .mood-buttons {
    justify-content: center;
  }

  .mood-button {
    min-width: 100px;
    height: 45px;
    font-size: 14px;
  }

  .comfort-chat-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}

/* 角色扮演样式 */
.roleplay-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.roleplay-character-selection {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.roleplay-header {
  text-align: center;
  margin-bottom: 30px;
}

.roleplay-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.roleplay-header p {
  color: #606266;
  font-size: 16px;
}

.character-filters {
  margin-bottom: 30px;
}

.filter-row {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-row .el-select,
.filter-row .el-input {
  min-width: 200px;
}

.character-list {
  margin-bottom: 30px;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.character-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
  display: flex;
  gap: 15px;
}

.character-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.character-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.character-avatar {
  flex-shrink: 0;
}

.character-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e4e7ed;
}

.character-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-info h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.character-category {
  color: #409eff;
  font-size: 12px;
  font-weight: 500;
  margin: 0;
}

.character-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.character-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.character-tags .tag {
  background: #f0f2f5;
  color: #606266;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.character-stats {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
}

.start-roleplay {
  text-align: center;
  margin-top: 30px;
}

/* 角色扮演聊天界面样式 */
.roleplay-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.roleplay-chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.character-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.character-avatar-small {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #409eff;
}

.character-details {
  display: flex;
  flex-direction: column;
}

.character-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.character-category {
  font-size: 12px;
  color: #409eff;
}

.roleplay-message-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
}

.roleplay-input-area {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: white;
}

/* 角色消息样式 */
.message-item.character {
  align-items: flex-start;
}

.message-item.character .message-text {
  background-color: #f0f9ff;
  color: #303133;
  border: 1px solid #409eff;
}

.message-item.character .message-avatar img {
  border-color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .character-grid {
    grid-template-columns: 1fr;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-row .el-select,
  .filter-row .el-input {
    min-width: auto;
    width: 100%;
  }

  .character-card {
    flex-direction: column;
    text-align: center;
  }

  .roleplay-chat-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}

/* 角色详情对话框样式 */
.character-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.character-detail-header {
  display: flex;
  gap: 15px;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.character-detail-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #409eff;
}

.character-detail-info {
  flex: 1;
}

.character-detail-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.character-detail-category {
  color: #409eff;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 10px 0;
}

.character-detail-stats {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #606266;
}

.character-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.detail-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.character-detail-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.detail-tag {
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}



/* 响应式设计 - 功能模块 */
@media (max-width: 768px) {
  .function-modules {
    padding: 20px 15px;
  }

  .modules-header h2 {
    font-size: 24px;
  }

  .modules-header p {
    font-size: 14px;
  }

  .modules-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .module-card {
    padding: 30px 20px;
  }

  .module-icon {
    font-size: 36px;
  }

  .module-header {
    padding: 12px 15px;
  }

  .module-header h3 {
    font-size: 14px;
  }
}
</style>
