<template>
  <div class="profile-container">
    <div class="profile-header">
      <h2>个人中心</h2>
      <p>管理您的个人信息和账户设置</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：头像和基本信息 -->
      <el-col :xs="24" :sm="24" :md="8">
        <el-card class="profile-card">
          <div class="avatar-section">
            <el-avatar :src="userInfo.avatar" :size="120" class="user-avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="avatar-upload">
              <el-upload
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :http-request="handleAvatarUpload"
                accept="image/*"
              >
                <el-button type="primary" size="small" :loading="avatarUploading">
                  {{ avatarUploading ? '上传中...' : '更换头像' }}
                </el-button>
              </el-upload>
            </div>
          </div>
          <div class="user-basic-info">
            <h3>{{ userInfo.name || userInfo.username }}</h3>
            <p class="user-username">@{{ userInfo.username }}</p>
            <div class="user-meta">
              <div class="meta-item">
                <span class="meta-label">注册时间：</span>
                <span class="meta-value">{{ formatDate(userInfo.createTime) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">最后更新：</span>
                <span class="meta-value">{{ formatDate(userInfo.updateTime) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：详细信息编辑 -->
      <el-col :xs="24" :sm="24" :md="16">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
              <el-button type="primary" @click="handleSave" :loading="saving">
                {{ saving ? '保存中...' : '保存' }}
              </el-button>
            </div>
          </template>
          
          <el-form
            ref="userFormRef"
            :model="userForm"
            :rules="userRules"
            label-width="80px"
            class="user-form"
          >
            <el-form-item label="姓名" prop="name">
              <el-input v-model="userForm.name" placeholder="请输入姓名" />
            </el-form-item>
            
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="userForm.phone" placeholder="请输入手机号" />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userForm.email" placeholder="请输入邮箱" />
            </el-form-item>
            
            <el-form-item label="地址" prop="address">
              <el-input
                v-model="userForm.address"
                type="textarea"
                :rows="3"
                placeholder="请输入地址"
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 密码修改 -->
        <el-card class="password-card">
          <template #header>
            <span>修改密码</span>
          </template>
          
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="80px"
            class="password-form"
          >
            <el-form-item label="原密码" prop="oldPassword">
              <el-input
                v-model="passwordForm.oldPassword"
                type="password"
                placeholder="请输入原密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handlePasswordChange" :loading="passwordChanging">
                {{ passwordChanging ? '修改中...' : '修改密码' }}
              </el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { getUserInfo, updateUser, updatePassword, uploadAvatar } from '../api/user'

const userFormRef = ref()
const passwordFormRef = ref()
const saving = ref(false)
const passwordChanging = ref(false)
const avatarUploading = ref(false)

// 用户信息
const userInfo = ref({})

// 用户信息表单
const userForm = reactive({
  name: '',
  phone: '',
  email: '',
  address: ''
})

// 密码修改表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const userRules = {
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getUserInfo()
    userInfo.value = res.data
    
    // 填充表单
    userForm.name = res.data.name || ''
    userForm.phone = res.data.phone || ''
    userForm.email = res.data.email || ''
    userForm.address = res.data.address || ''
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 保存用户信息
const handleSave = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    saving.value = true
    
    await updateUser(userForm)
    ElMessage.success('保存成功')
    
    // 重新获取用户信息
    await fetchUserInfo()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 修改密码
const handlePasswordChange = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    passwordChanging.value = true
    
    await updatePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword
    })
    
    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    console.error('密码修改失败:', error)
  } finally {
    passwordChanging.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  if (passwordFormRef.value) {
    passwordFormRef.value.clearValidate()
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 处理头像上传
const handleAvatarUpload = async ({ file }) => {
  try {
    avatarUploading.value = true
    const res = await uploadAvatar(file)
    
    ElMessage.success('头像上传成功')
    // 重新获取用户信息
    await fetchUserInfo()
  } catch (error) {
    console.error('头像上传失败:', error)
  } finally {
    avatarUploading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  margin-bottom: 20px;
}

.profile-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.profile-header p {
  color: #909399;
  margin: 0;
}

.profile-card {
  margin-bottom: 20px;
}

.avatar-section {
  text-align: center;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.user-avatar {
  margin-bottom: 16px;
}

.avatar-upload {
  margin-top: 10px;
}

.user-basic-info {
  text-align: center;
}

.user-basic-info h3 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 18px;
}

.user-username {
  color: #909399;
  font-size: 14px;
  margin-bottom: 16px;
}

.user-meta {
  text-align: left;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.meta-label {
  color: #909399;
}

.meta-value {
  color: #303133;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-form {
  margin-top: 20px;
}

.password-card {
  margin-bottom: 20px;
}

.password-form {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 0 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .user-form .el-form-item {
    margin-bottom: 16px;
  }

  .password-form .el-form-item {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .avatar-section {
    padding: 15px 0;
  }

  .user-avatar {
    width: 80px !important;
    height: 80px !important;
  }

  .meta-item {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
