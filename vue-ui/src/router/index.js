import { createRouter, createWebHistory } from 'vue-router'
import Layout from '../components/Layout.vue'
import Login from '../views/Login.vue'
import Home from '../views/Home.vue'
import Profile from '../views/Profile.vue'
import AIApp from '../views/AIApp.vue'
import Games from '../views/Games.vue'
import Programs from '../views/Programs.vue'

// 管理后台组件
import AdminLogin from '../views/AdminLogin.vue'
import AdminLayout from '../views/AdminLayout.vue'
import AdminDashboard from '../views/AdminDashboard.vue'
import AdminUsers from '../views/AdminUsers.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'Home',
        component: Home,
        meta: { title: '首页' }
      },
      {
        path: 'ai-app',
        name: '<PERSON>App',
        component: <PERSON>App,
        meta: { title: 'AI应用' }
      },
      {
        path: 'games',
        name: 'Games',
        component: Games,
        meta: { title: '小游戏' }
      },
      {
        path: 'programs',
        name: 'Programs',
        component: Programs,
        meta: { title: '程序' }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: Profile,
        meta: { title: '个人中心' }
      }
    ]
  },
  // 管理后台路由
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: { title: '管理员登录' }
  },
  {
    path: '/admin',
    component: AdminLayout,
    redirect: '/admin/dashboard',
    meta: { requiresAdmin: true },
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: AdminDashboard,
        meta: { title: '数据概览', requiresAdmin: true }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: AdminUsers,
        meta: { title: '用户管理', requiresAdmin: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = sessionStorage.getItem('isLoggedIn')
  const isAdminLoggedIn = sessionStorage.getItem('isAdminLoggedIn')

  // 管理后台路由处理
  if (to.path.startsWith('/admin')) {
    if (to.path === '/admin/login') {
      // 如果已经登录管理后台，跳转到dashboard
      if (isAdminLoggedIn) {
        next('/admin/dashboard')
      } else {
        next()
      }
    } else {
      // 需要管理员权限的页面
      if (!isAdminLoggedIn) {
        next('/admin/login')
      } else {
        next()
      }
    }
  } else {
    // 用户端路由处理
    if (to.path !== '/login' && !isLoggedIn) {
      next('/login')
    } else if (to.path === '/login' && isLoggedIn) {
      next('/')
    } else {
      next()
    }
  }
})

export default router
