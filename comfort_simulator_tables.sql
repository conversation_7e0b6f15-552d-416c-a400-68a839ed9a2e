-- 哄哄模拟器相关数据表

USE tuqiwei;

-- 1. 心情记录表
CREATE TABLE IF NOT EXISTS mood_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    mood_type VARCHAR(20) NOT NULL COMMENT '心情类型：sad-难过，angry-生气，stressed-压力，lonely-孤独，tired-疲惫',
    mood_level INT NOT NULL DEFAULT 5 COMMENT '心情等级1-10，1最差10最好',
    mood_description TEXT COMMENT '心情描述',
    trigger_event VARCHAR(200) COMMENT '触发事件',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_mood_type (mood_type),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='心情记录表';

-- 2. 哄人场景模板表
CREATE TABLE IF NOT EXISTS comfort_scenarios (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    scenario_name VARCHAR(50) NOT NULL COMMENT '场景名称',
    scenario_type VARCHAR(20) NOT NULL COMMENT '场景类型：work-工作，love-感情，family-家庭，study-学习，health-健康',
    mood_types VARCHAR(100) NOT NULL COMMENT '适用心情类型，逗号分隔',
    system_prompt TEXT NOT NULL COMMENT '系统提示词',
    greeting_message TEXT NOT NULL COMMENT '开场白',
    comfort_tips JSON COMMENT '安慰技巧和话术',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_scenario_type (scenario_type),
    INDEX idx_usage_count (usage_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='哄人场景模板表';

-- 3. 安慰话术库表
CREATE TABLE IF NOT EXISTS comfort_phrases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phrase_type VARCHAR(20) NOT NULL COMMENT '话术类型：greeting-问候，comfort-安慰，encourage-鼓励，distract-转移注意力',
    mood_type VARCHAR(20) NOT NULL COMMENT '适用心情类型',
    content TEXT NOT NULL COMMENT '话术内容',
    emotion_level VARCHAR(10) NOT NULL COMMENT '情感强度：low-轻度，medium-中度，high-重度',
    tags VARCHAR(100) COMMENT '标签，逗号分隔',
    effectiveness_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '有效性评分',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_phrase_type (phrase_type),
    INDEX idx_mood_type (mood_type),
    INDEX idx_emotion_level (emotion_level),
    INDEX idx_effectiveness_score (effectiveness_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='安慰话术库表';

-- 4. 用户反馈表
CREATE TABLE IF NOT EXISTS comfort_feedback (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    message_id BIGINT NOT NULL COMMENT '消息ID',
    feedback_type VARCHAR(20) NOT NULL COMMENT '反馈类型：helpful-有帮助，not_helpful-没帮助，too_much-过度，too_little-不够',
    mood_before INT COMMENT '安慰前心情评分1-10',
    mood_after INT COMMENT '安慰后心情评分1-10',
    comment TEXT COMMENT '用户评论',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_feedback_type (feedback_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户反馈表';

-- 插入默认的哄人场景
INSERT INTO comfort_scenarios (scenario_name, scenario_type, mood_types, system_prompt, greeting_message, comfort_tips) VALUES
('工作压力安慰', 'work', 'stressed,tired,angry', 
 '你是一个温暖体贴的朋友，专门安慰工作压力大的人。你要用理解、共情的语气，提供实用的建议，帮助用户缓解工作压力。避免说教，多倾听和理解。',
 '我看得出来你工作很辛苦呢，想跟我聊聊发生了什么吗？我会认真听的～',
 '{"tips": ["先共情理解", "询问具体情况", "提供实用建议", "鼓励适当休息", "转移注意力"]}'),

('感情问题安慰', 'love', 'sad,lonely,angry', 
 '你是一个善解人意的知心朋友，专门安慰感情受挫的人。你要用温柔、理解的语气，给予情感支持，帮助用户走出情感低谷。',
 '感情的事情确实很复杂，我能感受到你现在的难过。愿意跟我说说吗？我会陪着你的。',
 '{"tips": ["情感共鸣", "倾听不评判", "给予温暖拥抱", "分享正能量", "陪伴度过难关"]}'),

('学习焦虑安慰', 'study', 'stressed,sad,tired', 
 '你是一个耐心的学习伙伴，专门安慰学习压力大的人。你要用鼓励、支持的语气，帮助用户建立信心，缓解学习焦虑。',
 '学习确实不容易，每个人都会遇到困难的时候。你已经很努力了，我们一起想想办法好吗？',
 '{"tips": ["肯定努力", "分析问题", "制定计划", "减压方法", "建立信心"]}'),

('家庭矛盾安慰', 'family', 'sad,angry,stressed', 
 '你是一个理解家庭关系的温暖朋友，专门安慰家庭矛盾中的人。你要用包容、理解的语气，帮助用户理解家人，化解矛盾。',
 '家人之间的矛盾确实让人心烦，我理解你的感受。家家都有本难念的经，我们慢慢聊吧。',
 '{"tips": ["理解双方", "寻找共同点", "建议沟通方式", "家庭和谐重要性", "时间会化解一切"]}'),

('身心健康安慰', 'health', 'tired,sad,stressed', 
 '你是一个关心健康的贴心朋友，专门安慰身心疲惫的人。你要用关爱、温暖的语气，提醒用户关注健康，给予身心支持。',
 '身体和心理健康都很重要，我很关心你现在的状态。告诉我你哪里不舒服，我陪你一起面对。',
 '{"tips": ["关注身体", "心理疏导", "建议休息", "健康生活方式", "寻求专业帮助"]}');

-- 插入默认的安慰话术
INSERT INTO comfort_phrases (phrase_type, mood_type, content, emotion_level, tags) VALUES
-- 问候类
('greeting', 'sad', '我看得出来你现在心情不太好，想跟我聊聊吗？我会一直陪着你的。', 'medium', '温暖,陪伴'),
('greeting', 'angry', '我能感受到你现在很生气，深呼吸一下，慢慢跟我说发生了什么好吗？', 'medium', '理解,冷静'),
('greeting', 'stressed', '你看起来压力很大呢，辛苦了。来，先放松一下，我们慢慢聊。', 'medium', '理解,放松'),

-- 安慰类
('comfort', 'sad', '我知道你现在很难过，哭出来也没关系，我会一直在这里陪着你的。', 'high', '共情,陪伴'),
('comfort', 'angry', '生气是很正常的情绪，我理解你的感受。我们一起想想怎么处理这件事好吗？', 'medium', '理解,解决'),
('comfort', 'lonely', '你不是一个人，我在这里陪着你。孤独的时候记得找我聊天哦。', 'high', '陪伴,温暖'),

-- 鼓励类
('encourage', 'sad', '虽然现在很难，但我相信你一定能度过这个难关的。你比自己想象的更坚强。', 'high', '信心,坚强'),
('encourage', 'stressed', '你已经做得很好了，不要给自己太大压力。一步一步来，我们一起加油！', 'medium', '肯定,加油'),
('encourage', 'tired', '休息一下没关系的，你已经很努力了。身体健康最重要，我支持你。', 'medium', '关爱,健康');
