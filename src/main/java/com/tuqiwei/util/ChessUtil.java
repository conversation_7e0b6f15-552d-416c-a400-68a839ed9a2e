package com.tuqiwei.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 象棋游戏工具类
 */
public class ChessUtil {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 棋子类型常量
    public static final int EMPTY = 0;
    
    // 红方棋子 (1-7)
    public static final int RED_KING = 1;    // 帅
    public static final int RED_ADVISOR = 2; // 仕
    public static final int RED_BISHOP = 3;  // 相
    public static final int RED_KNIGHT = 4;  // 马
    public static final int RED_ROOK = 5;    // 车
    public static final int RED_CANNON = 6;  // 炮
    public static final int RED_PAWN = 7;    // 兵
    
    // 黑方棋子 (11-17)
    public static final int BLACK_KING = 11;    // 将
    public static final int BLACK_ADVISOR = 12; // 士
    public static final int BLACK_BISHOP = 13;  // 象
    public static final int BLACK_KNIGHT = 14;  // 马
    public static final int BLACK_ROOK = 15;    // 车
    public static final int BLACK_CANNON = 16;  // 炮
    public static final int BLACK_PAWN = 17;    // 卒
    
    // 棋子名称映射
    private static final String[] PIECE_NAMES = {
        "", "帅", "仕", "相", "马", "车", "炮", "兵",
        "", "", "", "将", "士", "象", "马", "车", "炮", "卒"
    };
    
    // 数字到中文的映射
    private static final String[] CHINESE_NUMBERS = {
        "", "一", "二", "三", "四", "五", "六", "七", "八", "九"
    };
    
    /**
     * 解析棋盘状态JSON字符串为二维数组
     */
    public static int[][] parseBoardState(String boardStateJson) {
        try {
            return objectMapper.readValue(boardStateJson, int[][].class);
        } catch (Exception e) {
            throw new RuntimeException("解析棋盘状态失败", e);
        }
    }
    
    /**
     * 将二维数组转换为JSON字符串
     */
    public static String boardStateToJson(int[][] board) {
        try {
            return objectMapper.writeValueAsString(board);
        } catch (Exception e) {
            throw new RuntimeException("转换棋盘状态失败", e);
        }
    }
    
    /**
     * 判断棋子是否属于红方
     */
    public static boolean isRedPiece(int piece) {
        return piece >= 1 && piece <= 7;
    }
    
    /**
     * 判断棋子是否属于黑方
     */
    public static boolean isBlackPiece(int piece) {
        return piece >= 11 && piece <= 17;
    }
    
    /**
     * 判断棋子是否属于指定玩家
     */
    public static boolean isPieceOfPlayer(int piece, int player) {
        if (player == 1) { // 红方
            return isRedPiece(piece);
        } else { // 黑方
            return isBlackPiece(piece);
        }
    }
    
    /**
     * 获取棋子名称
     */
    public static String getPieceName(int piece) {
        if (piece >= 0 && piece < PIECE_NAMES.length) {
            return PIECE_NAMES[piece];
        }
        return "";
    }
    
    /**
     * 检查坐标是否在棋盘范围内
     */
    public static boolean isValidPosition(int x, int y) {
        return x >= 0 && x < 10 && y >= 0 && y < 9;
    }
    
    /**
     * 检查位置是否在九宫格内
     */
    public static boolean isInPalace(int x, int y, int player) {
        if (player == 1) { // 红方九宫格
            return x >= 7 && x <= 9 && y >= 3 && y <= 5;
        } else { // 黑方九宫格
            return x >= 0 && x <= 2 && y >= 3 && y <= 5;
        }
    }
    
    /**
     * 检查相/象是否越河
     */
    public static boolean isBishopCrossRiver(int x, int player) {
        if (player == 1) { // 红方相不能过河
            return x < 5;
        } else { // 黑方象不能过河
            return x > 4;
        }
    }
    
    /**
     * 检查兵/卒是否可以横移
     */
    public static boolean canPawnMoveHorizontally(int x, int player) {
        if (player == 1) { // 红方兵过河后可以横移
            return x < 5;
        } else { // 黑方卒过河后可以横移
            return x > 4;
        }
    }
    
    /**
     * 计算两点之间的距离
     */
    public static int getDistance(int x1, int y1, int x2, int y2) {
        return Math.abs(x1 - x2) + Math.abs(y1 - y2);
    }
    
    /**
     * 检查路径是否被阻挡（用于车、炮的移动）
     */
    public static int countPiecesInPath(int[][] board, int fromX, int fromY, int toX, int toY) {
        int count = 0;
        
        if (fromX == toX) { // 垂直移动
            int start = Math.min(fromY, toY) + 1;
            int end = Math.max(fromY, toY);
            for (int y = start; y < end; y++) {
                if (board[fromX][y] != EMPTY) {
                    count++;
                }
            }
        } else if (fromY == toY) { // 水平移动
            int start = Math.min(fromX, toX) + 1;
            int end = Math.max(fromX, toX);
            for (int x = start; x < end; x++) {
                if (board[x][fromY] != EMPTY) {
                    count++;
                }
            }
        }
        
        return count;
    }
    
    /**
     * 生成移动的字符串表示（用于历史记录）
     */
    public static String generateMoveString(int fromX, int fromY, int toX, int toY) {
        return fromX + "," + fromY + "," + toX + "," + toY;
    }
    
    /**
     * 解析移动字符串
     */
    public static int[] parseMove(String moveString) {
        String[] parts = moveString.split(",");
        return new int[]{
            Integer.parseInt(parts[0]),
            Integer.parseInt(parts[1]),
            Integer.parseInt(parts[2]),
            Integer.parseInt(parts[3])
        };
    }
}
