package com.tuqiwei.util;

import static com.tuqiwei.util.ChessUtil.*;

/**
 * 象棋移动验证器
 */
public class ChessMoveValidator {
    
    /**
     * 验证移动是否合法
     */
    public static boolean isValidMove(int[][] board, int fromX, int fromY, int toX, int toY, int player) {
        // 检查坐标是否有效
        if (!isValidPosition(fromX, fromY) || !isValidPosition(toX, toY)) {
            return false;
        }
        
        // 检查起始位置是否有棋子
        int piece = board[fromX][fromY];
        if (piece == EMPTY) {
            return false;
        }
        
        // 检查棋子是否属于当前玩家
        if (!isPieceOfPlayer(piece, player)) {
            return false;
        }
        
        // 检查目标位置是否有己方棋子
        int targetPiece = board[toX][toY];
        if (targetPiece != EMPTY && isPieceOfPlayer(targetPiece, player)) {
            return false;
        }
        
        // 检查是否是原地不动
        if (fromX == toX && fromY == toY) {
            return false;
        }
        
        // 根据棋子类型验证移动规则
        return validatePieceMove(board, piece, fromX, fromY, toX, toY, player);
    }
    
    /**
     * 根据棋子类型验证移动规则
     */
    private static boolean validatePieceMove(int[][] board, int piece, int fromX, int fromY, int toX, int toY, int player) {
        switch (piece) {
            case RED_KING:
            case BLACK_KING:
                return validateKingMove(fromX, fromY, toX, toY, player);
                
            case RED_ADVISOR:
            case BLACK_ADVISOR:
                return validateAdvisorMove(fromX, fromY, toX, toY, player);
                
            case RED_BISHOP:
            case BLACK_BISHOP:
                return validateBishopMove(board, fromX, fromY, toX, toY, player);
                
            case RED_KNIGHT:
            case BLACK_KNIGHT:
                return validateKnightMove(board, fromX, fromY, toX, toY);
                
            case RED_ROOK:
            case BLACK_ROOK:
                return validateRookMove(board, fromX, fromY, toX, toY);
                
            case RED_CANNON:
            case BLACK_CANNON:
                return validateCannonMove(board, fromX, fromY, toX, toY);
                
            case RED_PAWN:
            case BLACK_PAWN:
                return validatePawnMove(fromX, fromY, toX, toY, player);
                
            default:
                return false;
        }
    }
    
    /**
     * 验证帅/将的移动
     */
    private static boolean validateKingMove(int fromX, int fromY, int toX, int toY, int player) {
        // 只能在九宫格内移动
        if (!isInPalace(toX, toY, player)) {
            return false;
        }
        
        // 只能移动一格，且只能横移或竖移
        int dx = Math.abs(toX - fromX);
        int dy = Math.abs(toY - fromY);
        return (dx == 1 && dy == 0) || (dx == 0 && dy == 1);
    }
    
    /**
     * 验证仕/士的移动
     */
    private static boolean validateAdvisorMove(int fromX, int fromY, int toX, int toY, int player) {
        // 只能在九宫格内移动
        if (!isInPalace(toX, toY, player)) {
            return false;
        }
        
        // 只能斜移一格
        int dx = Math.abs(toX - fromX);
        int dy = Math.abs(toY - fromY);
        return dx == 1 && dy == 1;
    }
    
    /**
     * 验证相/象的移动
     */
    private static boolean validateBishopMove(int[][] board, int fromX, int fromY, int toX, int toY, int player) {
        // 不能越河
        if (isBishopCrossRiver(toX, player)) {
            return false;
        }
        
        // 只能斜移两格
        int dx = toX - fromX;
        int dy = toY - fromY;
        if (Math.abs(dx) != 2 || Math.abs(dy) != 2) {
            return false;
        }
        
        // 检查象眼是否被堵
        int eyeX = fromX + dx / 2;
        int eyeY = fromY + dy / 2;
        return board[eyeX][eyeY] == EMPTY;
    }
    
    /**
     * 验证马的移动
     */
    private static boolean validateKnightMove(int[][] board, int fromX, int fromY, int toX, int toY) {
        int dx = toX - fromX;
        int dy = toY - fromY;
        
        // 马走日字
        if (!((Math.abs(dx) == 2 && Math.abs(dy) == 1) || (Math.abs(dx) == 1 && Math.abs(dy) == 2))) {
            return false;
        }
        
        // 检查马腿是否被堵
        int legX, legY;
        if (Math.abs(dx) == 2) {
            legX = fromX + dx / 2;
            legY = fromY;
        } else {
            legX = fromX;
            legY = fromY + dy / 2;
        }
        
        return board[legX][legY] == EMPTY;
    }
    
    /**
     * 验证车的移动
     */
    private static boolean validateRookMove(int[][] board, int fromX, int fromY, int toX, int toY) {
        // 车只能横移或竖移
        if (fromX != toX && fromY != toY) {
            return false;
        }
        
        // 检查路径是否被阻挡
        return countPiecesInPath(board, fromX, fromY, toX, toY) == 0;
    }
    
    /**
     * 验证炮的移动
     */
    private static boolean validateCannonMove(int[][] board, int fromX, int fromY, int toX, int toY) {
        // 炮只能横移或竖移
        if (fromX != toX && fromY != toY) {
            return false;
        }
        
        int piecesInPath = countPiecesInPath(board, fromX, fromY, toX, toY);
        int targetPiece = board[toX][toY];
        
        if (targetPiece == EMPTY) {
            // 移动到空位，路径必须无阻挡
            return piecesInPath == 0;
        } else {
            // 吃子，路径必须有且仅有一个棋子作为炮架
            return piecesInPath == 1;
        }
    }
    
    /**
     * 验证兵/卒的移动
     */
    private static boolean validatePawnMove(int fromX, int fromY, int toX, int toY, int player) {
        int dx = toX - fromX;
        int dy = toY - fromY;
        
        // 只能移动一格
        if (Math.abs(dx) + Math.abs(dy) != 1) {
            return false;
        }
        
        if (player == 1) { // 红方兵
            // 未过河只能向前
            if (fromX >= 5 && dx != -1) {
                return false;
            }
            // 过河后可以横移，但不能后退
            if (fromX < 5 && dx > 0) {
                return false;
            }
        } else { // 黑方卒
            // 未过河只能向前
            if (fromX <= 4 && dx != 1) {
                return false;
            }
            // 过河后可以横移，但不能后退
            if (fromX > 4 && dx < 0) {
                return false;
            }
        }
        
        return true;
    }
}
