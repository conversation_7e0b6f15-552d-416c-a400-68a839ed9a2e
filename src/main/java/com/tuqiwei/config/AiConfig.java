package com.tuqiwei.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * AI配置类
 */
@Configuration
@ConfigurationProperties(prefix = "ai.openai")
public class AiConfig {
    
    private String baseUrl;
    private String apiKey;
    private ChatOptions chat = new ChatOptions();
    
    @Bean
    public WebClient aiWebClient() {
        return WebClient.builder()
                .baseUrl(baseUrl)
                .defaultHeader("Authorization", "Bearer " + apiKey)
                .defaultHeader("Content-Type", "application/json")
                .build();
    }
    
    // Getter和Setter
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    public String getApiKey() {
        return apiKey;
    }
    
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
    
    public ChatOptions getChat() {
        return chat;
    }
    
    public void setChat(ChatOptions chat) {
        this.chat = chat;
    }
    
    /**
     * 聊天选项配置
     */
    public static class ChatOptions {
        private Options options = new Options();
        
        public Options getOptions() {
            return options;
        }
        
        public void setOptions(Options options) {
            this.options = options;
        }
        
        public static class Options {
            private String model = "deepseek-chat";
            private Double temperature = 0.7;
            private Integer maxTokens = 2000;
            private Boolean stream = true;
            
            public String getModel() {
                return model;
            }
            
            public void setModel(String model) {
                this.model = model;
            }
            
            public Double getTemperature() {
                return temperature;
            }
            
            public void setTemperature(Double temperature) {
                this.temperature = temperature;
            }
            
            public Integer getMaxTokens() {
                return maxTokens;
            }
            
            public void setMaxTokens(Integer maxTokens) {
                this.maxTokens = maxTokens;
            }
            
            public Boolean getStream() {
                return stream;
            }
            
            public void setStream(Boolean stream) {
                this.stream = stream;
            }
        }
    }
}
