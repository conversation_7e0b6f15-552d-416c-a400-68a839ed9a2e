package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 转盘历史记录实体
 */
public class WheelRecord {
    private Long id;
    private Long userId;
    private String wheelType;
    private String resultContent;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime spinTime;

    // 构造函数
    public WheelRecord() {}

    public WheelRecord(Long userId, String wheelType, String resultContent) {
        this.userId = userId;
        this.wheelType = wheelType;
        this.resultContent = resultContent;
        this.spinTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getWheelType() {
        return wheelType;
    }

    public void setWheelType(String wheelType) {
        this.wheelType = wheelType;
    }

    public String getResultContent() {
        return resultContent;
    }

    public void setResultContent(String resultContent) {
        this.resultContent = resultContent;
    }

    public LocalDateTime getSpinTime() {
        return spinTime;
    }

    public void setSpinTime(LocalDateTime spinTime) {
        this.spinTime = spinTime;
    }

    // 获取转盘类型的中文名称
    public String getWheelTypeName() {
        switch (wheelType) {
            case "eat": return "吃什么";
            case "play": return "玩什么";
            case "do": return "干什么";
            default: return wheelType;
        }
    }

    @Override
    public String toString() {
        return "WheelRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", wheelType='" + wheelType + '\'' +
                ", resultContent='" + resultContent + '\'' +
                ", spinTime=" + spinTime +
                '}';
    }
}
