package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 用户游戏记录实体
 */
public class UserGameRecord {
    private Long id;
    private Long userId;
    private String gameCode;
    private Long score;
    private Integer level;
    private String gameData;
    private Integer playTime;
    private Boolean isCompleted;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    // 构造函数
    public UserGameRecord() {}

    public UserGameRecord(Long userId, String gameCode, Long score, Integer level) {
        this.userId = userId;
        this.gameCode = gameCode;
        this.score = score;
        this.level = level;
        this.playTime = 0;
        this.isCompleted = false;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(String gameCode) {
        this.gameCode = gameCode;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getGameData() {
        return gameData;
    }

    public void setGameData(String gameData) {
        this.gameData = gameData;
    }

    public Integer getPlayTime() {
        return playTime;
    }

    public void setPlayTime(Integer playTime) {
        this.playTime = playTime;
    }

    public Boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "UserGameRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", gameCode='" + gameCode + '\'' +
                ", score=" + score +
                ", level=" + level +
                ", playTime=" + playTime +
                ", isCompleted=" + isCompleted +
                ", createdTime=" + createdTime +
                '}';
    }
}
