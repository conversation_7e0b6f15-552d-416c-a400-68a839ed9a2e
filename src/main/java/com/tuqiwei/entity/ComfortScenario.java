package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 哄人场景模板实体
 */
public class ComfortScenario {
    private Long id;
    private String scenarioName;
    private String scenarioType;
    private String moodTypes;
    private String systemPrompt;
    private String greetingMessage;
    private String comfortTips;
    private Boolean isActive;
    private Integer usageCount;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 构造函数
    public ComfortScenario() {}

    public ComfortScenario(String scenarioName, String scenarioType, String moodTypes, 
                          String systemPrompt, String greetingMessage) {
        this.scenarioName = scenarioName;
        this.scenarioType = scenarioType;
        this.moodTypes = moodTypes;
        this.systemPrompt = systemPrompt;
        this.greetingMessage = greetingMessage;
        this.isActive = true;
        this.usageCount = 0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getScenarioName() {
        return scenarioName;
    }

    public void setScenarioName(String scenarioName) {
        this.scenarioName = scenarioName;
    }

    public String getScenarioType() {
        return scenarioType;
    }

    public void setScenarioType(String scenarioType) {
        this.scenarioType = scenarioType;
    }

    public String getMoodTypes() {
        return moodTypes;
    }

    public void setMoodTypes(String moodTypes) {
        this.moodTypes = moodTypes;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public String getGreetingMessage() {
        return greetingMessage;
    }

    public void setGreetingMessage(String greetingMessage) {
        this.greetingMessage = greetingMessage;
    }

    public String getComfortTips() {
        return comfortTips;
    }

    public void setComfortTips(String comfortTips) {
        this.comfortTips = comfortTips;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getUsageCount() {
        return usageCount;
    }

    public void setUsageCount(Integer usageCount) {
        this.usageCount = usageCount;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "ComfortScenario{" +
                "id=" + id +
                ", scenarioName='" + scenarioName + '\'' +
                ", scenarioType='" + scenarioType + '\'' +
                ", moodTypes='" + moodTypes + '\'' +
                ", systemPrompt='" + systemPrompt + '\'' +
                ", greetingMessage='" + greetingMessage + '\'' +
                ", comfortTips='" + comfortTips + '\'' +
                ", isActive=" + isActive +
                ", usageCount=" + usageCount +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                '}';
    }
}
