package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 用户游戏统计实体
 */
public class UserGameStats {
    private Long id;
    private Long userId;
    private String gameCode;
    private Integer totalGames;
    private Long bestScore;
    private Long secondScore;
    private Long thirdScore;
    private Integer totalPlayTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastPlayTime;
    
    private String gameSettings;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 构造函数
    public UserGameStats() {}

    public UserGameStats(Long userId, String gameCode) {
        this.userId = userId;
        this.gameCode = gameCode;
        this.totalGames = 0;
        this.bestScore = 0L;
        this.secondScore = 0L;
        this.thirdScore = 0L;
        this.totalPlayTime = 0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(String gameCode) {
        this.gameCode = gameCode;
    }

    public Integer getTotalGames() {
        return totalGames;
    }

    public void setTotalGames(Integer totalGames) {
        this.totalGames = totalGames;
    }

    public Long getBestScore() {
        return bestScore;
    }

    public void setBestScore(Long bestScore) {
        this.bestScore = bestScore;
    }

    public Long getSecondScore() {
        return secondScore;
    }

    public void setSecondScore(Long secondScore) {
        this.secondScore = secondScore;
    }

    public Long getThirdScore() {
        return thirdScore;
    }

    public void setThirdScore(Long thirdScore) {
        this.thirdScore = thirdScore;
    }

    public Integer getTotalPlayTime() {
        return totalPlayTime;
    }

    public void setTotalPlayTime(Integer totalPlayTime) {
        this.totalPlayTime = totalPlayTime;
    }

    public LocalDateTime getLastPlayTime() {
        return lastPlayTime;
    }

    public void setLastPlayTime(LocalDateTime lastPlayTime) {
        this.lastPlayTime = lastPlayTime;
    }

    public String getGameSettings() {
        return gameSettings;
    }

    public void setGameSettings(String gameSettings) {
        this.gameSettings = gameSettings;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * 更新最高分记录
     */
    public void updateTopScores(Long newScore) {
        if (newScore > bestScore) {
            thirdScore = secondScore;
            secondScore = bestScore;
            bestScore = newScore;
        } else if (newScore > secondScore) {
            thirdScore = secondScore;
            secondScore = newScore;
        } else if (newScore > thirdScore) {
            thirdScore = newScore;
        }
    }

    @Override
    public String toString() {
        return "UserGameStats{" +
                "id=" + id +
                ", userId=" + userId +
                ", gameCode='" + gameCode + '\'' +
                ", totalGames=" + totalGames +
                ", bestScore=" + bestScore +
                ", secondScore=" + secondScore +
                ", thirdScore=" + thirdScore +
                ", totalPlayTime=" + totalPlayTime +
                ", lastPlayTime=" + lastPlayTime +
                '}';
    }
}
