package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 角色扮演会话实体
 */
public class RoleplaySession {
    private Long id;
    private Long userId;
    private Long characterId;
    private String sessionName;
    private String sessionContext;
    private Boolean isActive;
    private Integer messageCount;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastMessageTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 关联对象
    private RoleplayCharacter character;

    // 构造函数
    public RoleplaySession() {}

    public RoleplaySession(Long userId, Long characterId, String sessionName) {
        this.userId = userId;
        this.characterId = characterId;
        this.sessionName = sessionName;
        this.isActive = true;
        this.messageCount = 0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCharacterId() {
        return characterId;
    }

    public void setCharacterId(Long characterId) {
        this.characterId = characterId;
    }

    public String getSessionName() {
        return sessionName;
    }

    public void setSessionName(String sessionName) {
        this.sessionName = sessionName;
    }

    public String getSessionContext() {
        return sessionContext;
    }

    public void setSessionContext(String sessionContext) {
        this.sessionContext = sessionContext;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getMessageCount() {
        return messageCount;
    }

    public void setMessageCount(Integer messageCount) {
        this.messageCount = messageCount;
    }

    public LocalDateTime getLastMessageTime() {
        return lastMessageTime;
    }

    public void setLastMessageTime(LocalDateTime lastMessageTime) {
        this.lastMessageTime = lastMessageTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public RoleplayCharacter getCharacter() {
        return character;
    }

    public void setCharacter(RoleplayCharacter character) {
        this.character = character;
    }

    @Override
    public String toString() {
        return "RoleplaySession{" +
                "id=" + id +
                ", userId=" + userId +
                ", characterId=" + characterId +
                ", sessionName='" + sessionName + '\'' +
                ", messageCount=" + messageCount +
                ", lastMessageTime=" + lastMessageTime +
                '}';
    }
}
