package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 游戏进度实体
 */
public class GameProgress {
    private Long id;
    private Long userId;
    private String gameCode;
    private Long currentScore;
    private String gameBoard;
    private Integer gameLevel;
    private Integer playTime;
    private Boolean isPaused;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 构造函数
    public GameProgress() {}

    public GameProgress(Long userId, String gameCode, String gameBoard, Integer gameLevel) {
        this.userId = userId;
        this.gameCode = gameCode;
        this.currentScore = 0L;
        this.gameBoard = gameBoard;
        this.gameLevel = gameLevel;
        this.playTime = 0;
        this.isPaused = false;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(String gameCode) {
        this.gameCode = gameCode;
    }

    public Long getCurrentScore() {
        return currentScore;
    }

    public void setCurrentScore(Long currentScore) {
        this.currentScore = currentScore;
    }

    public String getGameBoard() {
        return gameBoard;
    }

    public void setGameBoard(String gameBoard) {
        this.gameBoard = gameBoard;
    }

    public Integer getGameLevel() {
        return gameLevel;
    }

    public void setGameLevel(Integer gameLevel) {
        this.gameLevel = gameLevel;
    }

    public Integer getPlayTime() {
        return playTime;
    }

    public void setPlayTime(Integer playTime) {
        this.playTime = playTime;
    }

    public Boolean getIsPaused() {
        return isPaused;
    }

    public void setIsPaused(Boolean isPaused) {
        this.isPaused = isPaused;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "GameProgress{" +
                "id=" + id +
                ", userId=" + userId +
                ", gameCode='" + gameCode + '\'' +
                ", currentScore=" + currentScore +
                ", gameLevel=" + gameLevel +
                ", playTime=" + playTime +
                ", isPaused=" + isPaused +
                ", updatedTime=" + updatedTime +
                '}';
    }
}
