package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 象棋游戏实体
 */
public class ChessGame {
    private String gameId;
    private Long userId;
    private String boardState;  // JSON格式的棋盘状态
    private int currentPlayer;  // 当前玩家 1=红方 2=黑方
    private String gameStatus;  // PLAYING, CHECKMATE, STALEMATE, DRAW, SURRENDER
    private List<String> moveHistory;  // 移动历史记录
    private List<String> notationHistory;  // 棋谱记录
    private boolean undoRequested;  // 是否有悔棋请求
    private int undoRequestPlayer;  // 请求悔棋的玩家
    private long startTime;  // 游戏开始时间
    private long totalTime;  // 总游戏时长（毫秒）
    private String winner;  // 获胜方
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 构造函数
    public ChessGame() {}

    public ChessGame(String gameId, Long userId) {
        this.gameId = gameId;
        this.userId = userId;
        this.currentPlayer = 1; // 红方先行
        this.gameStatus = "PLAYING";
        this.undoRequested = false;
        this.startTime = System.currentTimeMillis();
        this.totalTime = 0;
        // 初始化棋盘状态
        this.boardState = initializeBoardState();
    }

    /**
     * 初始化棋盘状态
     */
    private String initializeBoardState() {
        // 返回标准象棋开局的JSON字符串
        // 棋盘用10x9的二维数组表示，0表示空位
        // 红方：1-7 (帅1,仕2,相3,马4,车5,炮6,兵7)
        // 黑方：11-17 (将11,士12,象13,马14,车15,炮16,卒17)
        return "[[15,14,13,12,11,12,13,14,15]," +
               "[0,0,0,0,0,0,0,0,0]," +
               "[0,16,0,0,0,0,0,16,0]," +
               "[17,0,17,0,17,0,17,0,17]," +
               "[0,0,0,0,0,0,0,0,0]," +
               "[0,0,0,0,0,0,0,0,0]," +
               "[7,0,7,0,7,0,7,0,7]," +
               "[0,6,0,0,0,0,0,6,0]," +
               "[0,0,0,0,0,0,0,0,0]," +
               "[5,4,3,2,1,2,3,4,5]]";
    }

    // Getter和Setter方法
    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getBoardState() {
        return boardState;
    }

    public void setBoardState(String boardState) {
        this.boardState = boardState;
    }

    public int getCurrentPlayer() {
        return currentPlayer;
    }

    public void setCurrentPlayer(int currentPlayer) {
        this.currentPlayer = currentPlayer;
    }

    public String getGameStatus() {
        return gameStatus;
    }

    public void setGameStatus(String gameStatus) {
        this.gameStatus = gameStatus;
    }

    public List<String> getMoveHistory() {
        return moveHistory;
    }

    public void setMoveHistory(List<String> moveHistory) {
        this.moveHistory = moveHistory;
    }

    public List<String> getNotationHistory() {
        return notationHistory;
    }

    public void setNotationHistory(List<String> notationHistory) {
        this.notationHistory = notationHistory;
    }

    public boolean isUndoRequested() {
        return undoRequested;
    }

    public void setUndoRequested(boolean undoRequested) {
        this.undoRequested = undoRequested;
    }

    public int getUndoRequestPlayer() {
        return undoRequestPlayer;
    }

    public void setUndoRequestPlayer(int undoRequestPlayer) {
        this.undoRequestPlayer = undoRequestPlayer;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }

    public String getWinner() {
        return winner;
    }

    public void setWinner(String winner) {
        this.winner = winner;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "ChessGame{" +
                "gameId='" + gameId + '\'' +
                ", userId=" + userId +
                ", currentPlayer=" + currentPlayer +
                ", gameStatus='" + gameStatus + '\'' +
                ", undoRequested=" + undoRequested +
                ", totalTime=" + totalTime +
                ", winner='" + winner + '\'' +
                '}';
    }
}
