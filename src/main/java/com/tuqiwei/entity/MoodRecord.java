package com.tuqiwei.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 心情记录实体
 */
public class MoodRecord {
    private Long id;
    private Long userId;
    private Long sessionId;
    private String moodType;
    private Integer moodLevel;
    private String moodDescription;
    private String triggerEvent;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    // 构造函数
    public MoodRecord() {}

    public MoodRecord(Long userId, Long sessionId, String moodType, Integer moodLevel) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.moodType = moodType;
        this.moodLevel = moodLevel;
    }

    public MoodRecord(Long userId, Long sessionId, String moodType, Integer moodLevel, 
                     String moodDescription, String triggerEvent) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.moodType = moodType;
        this.moodLevel = moodLevel;
        this.moodDescription = moodDescription;
        this.triggerEvent = triggerEvent;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public String getMoodType() {
        return moodType;
    }

    public void setMoodType(String moodType) {
        this.moodType = moodType;
    }

    public Integer getMoodLevel() {
        return moodLevel;
    }

    public void setMoodLevel(Integer moodLevel) {
        this.moodLevel = moodLevel;
    }

    public String getMoodDescription() {
        return moodDescription;
    }

    public void setMoodDescription(String moodDescription) {
        this.moodDescription = moodDescription;
    }

    public String getTriggerEvent() {
        return triggerEvent;
    }

    public void setTriggerEvent(String triggerEvent) {
        this.triggerEvent = triggerEvent;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "MoodRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", sessionId=" + sessionId +
                ", moodType='" + moodType + '\'' +
                ", moodLevel=" + moodLevel +
                ", moodDescription='" + moodDescription + '\'' +
                ", triggerEvent='" + triggerEvent + '\'' +
                ", createdTime=" + createdTime +
                '}';
    }
}
