package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.service.OSSService;
import com.tuqiwei.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/file")
public class FileController {

    @Autowired
    private OSSService ossService;

    @Autowired
    private UserService userService;

    /**
     * 上传头像
     */
    @PostMapping("/upload/avatar")
    public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error(401, "未登录");
        }

        if (file.isEmpty()) {
            return Result.error("请选择要上传的文件");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return Result.error("只能上传图片文件");
        }

        // 检查文件大小（限制为5MB）
        if (file.getSize() > 5 * 1024 * 1024) {
            return Result.error("文件大小不能超过5MB");
        }

        try {
            // 上传到OSS
            String avatarUrl = ossService.uploadFile(file);
            
            // 更新用户头像
            boolean success = userService.updateAvatar(userId, avatarUrl);
            if (success) {
                Map<String, String> result = new HashMap<>();
                result.put("avatarUrl", avatarUrl);
                return Result.success("头像上传成功", result);
            } else {
                return Result.error("头像上传失败");
            }
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return Result.error("头像上传失败：" + e.getMessage());
        }
    }
}
