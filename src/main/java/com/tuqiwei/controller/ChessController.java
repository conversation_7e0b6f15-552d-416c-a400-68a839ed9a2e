package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.service.ChessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 象棋游戏控制器
 */
@RestController
@RequestMapping("/api/chess")
public class ChessController {
    
    @Autowired
    private ChessService chessService;
    
    /**
     * 开始新的象棋游戏
     */
    @PostMapping("/start")
    public Result<Map<String, Object>> startNewGame(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Map<String, Object> result = chessService.startNewChessGame(userId);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("开始游戏失败：" + e.getMessage());
        }
    }
    
    /**
     * 移动棋子
     */
    @PostMapping("/move")
    public Result<Map<String, Object>> movePiece(@RequestBody Map<String, Object> request, 
                                                 HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String gameId = request.get("gameId").toString();
            int fromX = Integer.parseInt(request.get("fromX").toString());
            int fromY = Integer.parseInt(request.get("fromY").toString());
            int toX = Integer.parseInt(request.get("toX").toString());
            int toY = Integer.parseInt(request.get("toY").toString());
            
            Map<String, Object> result = chessService.movePiece(userId, gameId, fromX, fromY, toX, toY);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("移动失败：" + e.getMessage());
        }
    }
    
    /**
     * 请求悔棋
     */
    @PostMapping("/undo/request")
    public Result<Map<String, Object>> requestUndo(@RequestBody Map<String, Object> request,
                                                   HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String gameId = request.get("gameId").toString();
            Map<String, Object> result = chessService.requestUndo(userId, gameId);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("悔棋请求失败：" + e.getMessage());
        }
    }
    
    /**
     * 响应悔棋请求
     */
    @PostMapping("/undo/respond")
    public Result<Map<String, Object>> respondUndo(@RequestBody Map<String, Object> request,
                                                   HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String gameId = request.get("gameId").toString();
            boolean accept = Boolean.parseBoolean(request.get("accept").toString());
            
            Map<String, Object> result = chessService.respondUndo(userId, gameId, accept);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("处理悔棋响应失败：" + e.getMessage());
        }
    }
    
    /**
     * 认输
     */
    @PostMapping("/surrender")
    public Result<Map<String, Object>> surrender(@RequestBody Map<String, Object> request,
                                                 HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String gameId = request.get("gameId").toString();
            Map<String, Object> result = chessService.surrender(userId, gameId);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("认输失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存棋局
     */
    @PostMapping("/save")
    public Result<Map<String, Object>> saveGame(@RequestBody Map<String, Object> request,
                                                HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String gameId = request.get("gameId").toString();
            String gameName = request.get("gameName").toString();
            
            Map<String, Object> result = chessService.saveGame(userId, gameId, gameName);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("保存棋局失败：" + e.getMessage());
        }
    }
    
    /**
     * 加载棋局
     */
    @PostMapping("/load")
    public Result<Map<String, Object>> loadGame(@RequestBody Map<String, Object> request,
                                                HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Long gameRecordId = Long.parseLong(request.get("gameRecordId").toString());
            Map<String, Object> result = chessService.loadGame(userId, gameRecordId);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("加载棋局失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户的象棋游戏记录
     */
    @GetMapping("/records")
    public Result<List<Map<String, Object>>> getUserChessGames(@RequestParam(defaultValue = "10") int limit,
                                                               HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<Map<String, Object>> games = chessService.getUserChessGames(userId, limit);
            return Result.success(games);
        } catch (Exception e) {
            return Result.error("获取游戏记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取游戏状态
     */
    @GetMapping("/state/{gameId}")
    public Result<Map<String, Object>> getGameState(@PathVariable String gameId,
                                                    HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Map<String, Object> result = chessService.getGameState(gameId);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("获取游戏状态失败：" + e.getMessage());
        }
    }
}
