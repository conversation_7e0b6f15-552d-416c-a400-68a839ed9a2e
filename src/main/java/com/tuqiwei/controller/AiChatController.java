package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.entity.ChatMessage;
import com.tuqiwei.entity.ChatSession;
import com.tuqiwei.service.AiChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * AI聊天控制器
 */
@RestController
@RequestMapping("/api/ai")
@CrossOrigin(origins = "http://localhost:3000", allowCredentials = "true")
public class AiChatController {
    
    @Autowired
    private AiChatService aiChatService;
    
    /**
     * 发送消息（流式响应）
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> sendMessageStream(@RequestBody Map<String, Object> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Flux.just("data: {\"error\": \"用户未登录\"}\n\n");
        }
        
        Long sessionId = Long.valueOf(request.get("sessionId").toString());
        String message = request.get("message").toString();
        
        return aiChatService.sendMessage(sessionId, message, userId)
                .map(content -> "data: {\"content\": \"" + escapeJson(content) + "\"}\n\n")
                .concatWith(Flux.just("data: [DONE]\n\n"));
    }
    
    /**
     * 发送消息（同步响应）
     */
    @PostMapping("/chat/send")
    public Result<String> sendMessage(@RequestBody Map<String, Object> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Long sessionId = Long.valueOf(request.get("sessionId").toString());
            String message = request.get("message").toString();
            
            String reply = aiChatService.sendMessageSync(sessionId, message, userId);
            return Result.success(reply);
            
        } catch (Exception e) {
            return Result.error("发送消息失败：" + e.getMessage());
        }
    }
    
    /**
     * 创建新会话
     */
    @PostMapping("/sessions")
    public Result<ChatSession> createSession(@RequestBody Map<String, String> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String sessionName = request.getOrDefault("sessionName", "新对话");
            String sessionType = request.getOrDefault("sessionType", "chat");
            
            ChatSession chatSession = aiChatService.createSession(userId, sessionName, sessionType);
            return Result.success(chatSession);
            
        } catch (Exception e) {
            return Result.error("创建会话失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户会话列表
     */
    @GetMapping("/sessions")
    public Result<List<ChatSession>> getUserSessions(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<ChatSession> sessions = aiChatService.getUserSessions(userId);
            return Result.success(sessions);
            
        } catch (Exception e) {
            return Result.error("获取会话列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取会话消息历史
     */
    @GetMapping("/sessions/{sessionId}/messages")
    public Result<List<ChatMessage>> getSessionMessages(@PathVariable Long sessionId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<ChatMessage> messages = aiChatService.getSessionMessages(sessionId);
            return Result.success(messages);
            
        } catch (Exception e) {
            return Result.error("获取消息历史失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除会话
     */
    @DeleteMapping("/sessions/{sessionId}")
    public Result<Boolean> deleteSession(@PathVariable Long sessionId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = aiChatService.deleteSession(sessionId, userId);
            return success ? Result.success(true) : Result.error("删除会话失败");
            
        } catch (Exception e) {
            return Result.error("删除会话失败：" + e.getMessage());
        }
    }
    
    /**
     * 重命名会话
     */
    @PutMapping("/sessions/{sessionId}/name")
    public Result<Boolean> renameSession(@PathVariable Long sessionId, 
                                       @RequestBody Map<String, String> request, 
                                       HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String newName = request.get("sessionName");
            boolean success = aiChatService.renameSession(sessionId, newName, userId);
            return success ? Result.success(true) : Result.error("重命名会话失败");
            
        } catch (Exception e) {
            return Result.error("重命名会话失败：" + e.getMessage());
        }
    }
    
    /**
     * 收藏/取消收藏消息
     */
    @PutMapping("/messages/{messageId}/favorite")
    public Result<Boolean> toggleMessageFavorite(@PathVariable Long messageId,
                                                @RequestBody Map<String, Boolean> request,
                                                HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Boolean isFavorite = request.get("isFavorite");
            boolean success = aiChatService.toggleMessageFavorite(messageId, userId, isFavorite);
            return success ? Result.success(true) : Result.error("操作失败");
            
        } catch (Exception e) {
            return Result.error("操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取收藏的消息
     */
    @GetMapping("/messages/favorites")
    public Result<List<ChatMessage>> getFavoriteMessages(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<ChatMessage> messages = aiChatService.getFavoriteMessages(userId);
            return Result.success(messages);
            
        } catch (Exception e) {
            return Result.error("获取收藏消息失败：" + e.getMessage());
        }
    }
    
    /**
     * 转义JSON字符串
     */
    private String escapeJson(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
