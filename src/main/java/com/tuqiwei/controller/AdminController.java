package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.entity.AdminUser;
import com.tuqiwei.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
public class AdminController {
    
    @Autowired
    private AdminService adminService;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginData, HttpSession session) {
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");
            
            if (username == null || username.trim().isEmpty()) {
                return Result.error("用户名不能为空");
            }
            
            if (password == null || password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }
            
            Map<String, Object> result = adminService.login(username, password);
            
            if ((Boolean) result.get("success")) {
                AdminUser admin = (AdminUser) result.get("adminInfo");
                session.setAttribute("adminId", admin.getId());
                session.setAttribute("adminUsername", admin.getUsername());
                
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }
    
    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public Result<String> logout(HttpSession session) {
        try {
            session.removeAttribute("adminId");
            session.removeAttribute("adminUsername");
            return Result.success("登出成功");
        } catch (Exception e) {
            return Result.error("登出失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取管理员信息
     */
    @GetMapping("/info")
    public Result<AdminUser> getAdminInfo(HttpSession session) {
        Long adminId = (Long) session.getAttribute("adminId");
        if (adminId == null) {
            return Result.error("未登录");
        }
        
        try {
            AdminUser admin = adminService.getAdminInfo(adminId);
            if (admin != null) {
                return Result.success(admin);
            } else {
                return Result.error("获取管理员信息失败");
            }
        } catch (Exception e) {
            return Result.error("获取管理员信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取数据统计概览
     */
    @GetMapping("/dashboard/stats")
    public Result<Map<String, Object>> getDashboardStats(HttpSession session) {
        Long adminId = (Long) session.getAttribute("adminId");
        if (adminId == null) {
            return Result.error("未登录");
        }
        
        try {
            Map<String, Object> stats = adminService.getDashboardStats();
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户趋势数据
     */
    @GetMapping("/dashboard/trends")
    public Result<Map<String, Object>> getUserTrends(HttpSession session) {
        Long adminId = (Long) session.getAttribute("adminId");
        if (adminId == null) {
            return Result.error("未登录");
        }
        
        try {
            Map<String, Object> trends = adminService.getUserTrendData();
            return Result.success(trends);
        } catch (Exception e) {
            return Result.error("获取趋势数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public Result<Map<String, Object>> getUserList(@RequestParam(defaultValue = "") String keyword,
                                                  @RequestParam(required = false) Boolean status,
                                                  @RequestParam(defaultValue = "1") int page,
                                                  @RequestParam(defaultValue = "10") int size,
                                                  HttpSession session) {
        Long adminId = (Long) session.getAttribute("adminId");
        if (adminId == null) {
            return Result.error("未登录");
        }
        
        try {
            // 限制每页大小
            if (size > 100) {
                size = 100;
            }
            if (size < 1) {
                size = 10;
            }
            
            Map<String, Object> result = adminService.getUserList(keyword, status, page, size);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取用户列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新用户状态
     */
    @PutMapping("/users/{userId}/status")
    public Result<String> updateUserStatus(@PathVariable Long userId,
                                         @RequestBody Map<String, Boolean> statusData,
                                         HttpSession session) {
        Long adminId = (Long) session.getAttribute("adminId");
        if (adminId == null) {
            return Result.error("未登录");
        }
        
        try {
            Boolean status = statusData.get("status");
            if (status == null) {
                return Result.error("状态参数不能为空");
            }
            
            boolean success = adminService.updateUserStatus(userId, status);
            
            if (success) {
                return Result.success(status ? "用户已启用" : "用户已禁用");
            } else {
                return Result.error("更新用户状态失败");
            }
        } catch (Exception e) {
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }
}
