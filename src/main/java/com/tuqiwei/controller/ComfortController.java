package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.entity.ComfortScenario;
import com.tuqiwei.entity.MoodRecord;
import com.tuqiwei.service.ComfortService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 哄哄模拟器控制器
 */
@RestController
@RequestMapping("/api/comfort")
@CrossOrigin(origins = "http://localhost:3000", allowCredentials = "true")
public class ComfortController {
    
    @Autowired
    private ComfortService comfortService;
    
    /**
     * 获取所有可用的安慰场景
     */
    @GetMapping("/scenarios")
    public Result<List<ComfortScenario>> getScenarios() {
        try {
            List<ComfortScenario> scenarios = comfortService.getAvailableScenarios();
            return Result.success(scenarios);
        } catch (Exception e) {
            return Result.error("获取场景失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据心情推荐场景
     */
    @GetMapping("/scenarios/recommend")
    public Result<List<ComfortScenario>> recommendScenarios(@RequestParam String moodType) {
        try {
            List<ComfortScenario> scenarios = comfortService.recommendScenarios(moodType);
            return Result.success(scenarios);
        } catch (Exception e) {
            return Result.error("推荐场景失败：" + e.getMessage());
        }
    }
    
    /**
     * 分析用户情绪
     */
    @PostMapping("/analyze-mood")
    public Result<String> analyzeMood(@RequestBody Map<String, String> request) {
        try {
            String userMessage = request.get("message");
            String moodType = comfortService.analyzeMood(userMessage);
            return Result.success(moodType);
        } catch (Exception e) {
            return Result.error("情绪分析失败：" + e.getMessage());
        }
    }
    
    /**
     * 记录用户心情
     */
    @PostMapping("/mood-record")
    public Result<Boolean> recordMood(@RequestBody Map<String, Object> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Long sessionId = Long.valueOf(request.get("sessionId").toString());
            String moodType = (String) request.get("moodType");
            Integer moodLevel = Integer.valueOf(request.get("moodLevel").toString());
            String description = (String) request.get("description");
            String triggerEvent = (String) request.get("triggerEvent");
            
            comfortService.recordMood(userId, sessionId, moodType, moodLevel, description, triggerEvent);
            return Result.success(true);
        } catch (Exception e) {
            return Result.error("记录心情失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户心情历史
     */
    @GetMapping("/mood-history")
    public Result<List<MoodRecord>> getMoodHistory(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<MoodRecord> history = comfortService.getUserMoodHistory(userId);
            return Result.success(history);
        } catch (Exception e) {
            return Result.error("获取心情历史失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户心情统计
     */
    @GetMapping("/mood-stats")
    public Result<Map<String, Object>> getMoodStats(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Map<String, Object> stats = comfortService.getUserMoodStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取心情统计失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成安慰回复
     */
    @PostMapping("/generate-reply")
    public Result<String> generateReply(@RequestBody Map<String, Object> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Long scenarioId = Long.valueOf(request.get("scenarioId").toString());
            String userMessage = (String) request.get("userMessage");
            String moodType = (String) request.get("moodType");
            
            // 更新场景使用次数
            comfortService.updateScenarioUsage(scenarioId);
            
            // 生成回复
            String reply = comfortService.generateComfortReply(scenarioId, userMessage, moodType);
            return Result.success(reply);
        } catch (Exception e) {
            return Result.error("生成回复失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取场景问候语
     */
    @GetMapping("/scenarios/{scenarioId}/greeting")
    public Result<String> getScenarioGreeting(@PathVariable Long scenarioId) {
        try {
            String greeting = comfortService.getScenarioGreeting(scenarioId);
            return Result.success(greeting);
        } catch (Exception e) {
            return Result.error("获取问候语失败：" + e.getMessage());
        }
    }
    
    /**
     * 开始安慰会话
     */
    @PostMapping("/start-session")
    public Result<Map<String, Object>> startComfortSession(@RequestBody Map<String, Object> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Long scenarioId = Long.valueOf(request.get("scenarioId").toString());
            String moodType = (String) request.get("moodType");
            Integer moodLevel = (Integer) request.get("moodLevel");
            
            // 获取场景问候语
            String greeting = comfortService.getScenarioGreeting(scenarioId);
            
            // 更新场景使用次数
            comfortService.updateScenarioUsage(scenarioId);
            
            Map<String, Object> response = Map.of(
                "greeting", greeting,
                "scenarioId", scenarioId,
                "moodType", moodType
            );
            
            return Result.success(response);
        } catch (Exception e) {
            return Result.error("开始会话失败：" + e.getMessage());
        }
    }
}
