package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.entity.AiAvatar;
import com.tuqiwei.entity.User;
import com.tuqiwei.mapper.AiAvatarMapper;
import com.tuqiwei.mapper.UserMapper;
import com.tuqiwei.service.OSSService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 头像管理控制器
 */
@RestController
@RequestMapping("/api/avatar")
@CrossOrigin(origins = "http://localhost:3000", allowCredentials = "true")
public class AvatarController {
    
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AiAvatarMapper aiAvatarMapper;

    @Autowired
    private OSSService ossService;
    
    /**
     * 上传用户头像
     */
    @PostMapping("/user/upload")
    public Result<String> uploadUserAvatar(@RequestParam("file") MultipartFile file, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        if (file.isEmpty()) {
            return Result.error("请选择要上传的文件");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return Result.error("只能上传图片文件");
        }

        // 检查文件大小（限制为5MB）
        if (file.getSize() > 5 * 1024 * 1024) {
            return Result.error("文件大小不能超过5MB");
        }

        try {
            // 上传到OSS
            String avatarUrl = ossService.uploadFile(file);

            // 更新用户头像
            userMapper.updateAvatar(userId, avatarUrl);

            return Result.success(avatarUrl);

        } catch (Exception e) {
            return Result.error("头像上传失败：" + e.getMessage());
        }
    }
    
    /**
     * 上传AI头像
     */
    @PostMapping("/ai/upload")
    public Result<AiAvatar> uploadAiAvatar(@RequestParam("file") MultipartFile file,
                                          @RequestParam("avatarName") String avatarName,
                                          HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        if (file.isEmpty()) {
            return Result.error("请选择要上传的文件");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return Result.error("只能上传图片文件");
        }

        // 检查文件大小（限制为5MB）
        if (file.getSize() > 5 * 1024 * 1024) {
            return Result.error("文件大小不能超过5MB");
        }

        try {
            // 上传到OSS
            String avatarUrl = ossService.uploadFile(file);

            // 保存AI头像记录
            AiAvatar aiAvatar = new AiAvatar(userId, avatarName, avatarUrl, false);
            aiAvatarMapper.insert(aiAvatar);

            return Result.success(aiAvatar);

        } catch (Exception e) {
            return Result.error("AI头像上传失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户头像信息
     */
    @GetMapping("/user/info")
    public Result<Map<String, String>> getUserAvatarInfo(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            User user = userMapper.selectById(userId);
            String avatarUrl = user.getAvatar() != null ? user.getAvatar() : "https://cqwm-tqw.oss-cn-hangzhou.aliyuncs.com/avatar/default-user.png";

            return Result.success(Map.of("avatarUrl", avatarUrl));

        } catch (Exception e) {
            return Result.error("获取用户头像失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取AI头像列表
     */
    @GetMapping("/ai/list")
    public Result<List<AiAvatar>> getAiAvatarList(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<AiAvatar> avatars = aiAvatarMapper.selectByUserId(userId);
            return Result.success(avatars);
            
        } catch (Exception e) {
            return Result.error("获取AI头像列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取默认AI头像
     */
    @GetMapping("/ai/default")
    public Result<String> getDefaultAiAvatar(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            AiAvatar defaultAvatar = aiAvatarMapper.selectDefaultByUserId(userId);
            String avatarUrl = defaultAvatar != null ? defaultAvatar.getAvatarUrl() : "https://cqwm-tqw.oss-cn-hangzhou.aliyuncs.com/avatar/default-ai.png";

            return Result.success(avatarUrl);

        } catch (Exception e) {
            return Result.error("获取默认AI头像失败：" + e.getMessage());
        }
    }
    
    /**
     * 设置默认AI头像
     */
    @PutMapping("/ai/default/{avatarId}")
    public Result<Boolean> setDefaultAiAvatar(@PathVariable Long avatarId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            // 先取消所有默认头像
            aiAvatarMapper.clearDefaultAvatars(userId);
            // 设置新的默认头像
            int result = aiAvatarMapper.setDefaultAvatar(userId, avatarId);
            
            return result > 0 ? Result.success(true) : Result.error("设置失败");
            
        } catch (Exception e) {
            return Result.error("设置默认AI头像失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除AI头像
     */
    @DeleteMapping("/ai/{avatarId}")
    public Result<Boolean> deleteAiAvatar(@PathVariable Long avatarId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            int result = aiAvatarMapper.deleteById(avatarId);
            return result > 0 ? Result.success(true) : Result.error("删除失败");
            
        } catch (Exception e) {
            return Result.error("删除AI头像失败：" + e.getMessage());
        }
    }

}
