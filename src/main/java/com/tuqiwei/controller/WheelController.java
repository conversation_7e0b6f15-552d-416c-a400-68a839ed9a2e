package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.entity.WheelConfig;
import com.tuqiwei.service.WheelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 转盘控制器
 */
@RestController
@RequestMapping("/api/wheel")
public class WheelController {
    
    @Autowired
    private WheelService wheelService;
    
    /**
     * 获取转盘类型列表
     */
    @GetMapping("/types")
    public Result<List<Map<String, Object>>> getWheelTypes() {
        try {
            List<Map<String, Object>> types = wheelService.getWheelTypes();
            return Result.success(types);
        } catch (Exception e) {
            return Result.error("获取转盘类型失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户转盘配置
     */
    @GetMapping("/{wheelType}/config")
    public Result<List<WheelConfig>> getUserWheelConfig(@PathVariable String wheelType, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<WheelConfig> configs = wheelService.getUserWheelConfigs(userId, wheelType);
            return Result.success(configs);
        } catch (Exception e) {
            return Result.error("获取转盘配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存用户转盘配置
     */
    @PostMapping("/{wheelType}/config")
    public Result<String> saveUserWheelConfig(@PathVariable String wheelType, 
                                            @RequestBody List<WheelConfig> configs,
                                            HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            // 验证配置数据
            if (configs == null || configs.isEmpty()) {
                return Result.error("配置数据不能为空");
            }
            
            if (configs.size() > 12) {
                return Result.error("转盘最多支持12个选项");
            }
            
            // 过滤空内容的配置
            configs.removeIf(config -> config.getContent() == null || config.getContent().trim().isEmpty());
            
            if (configs.isEmpty()) {
                return Result.error("至少需要一个有效的转盘选项");
            }
            
            boolean success = wheelService.saveUserWheelConfigs(userId, wheelType, configs);
            
            if (success) {
                return Result.success("保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            return Result.error("保存转盘配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 转盘旋转
     */
    @PostMapping("/{wheelType}/spin")
    public Result<Map<String, Object>> spinWheel(@PathVariable String wheelType, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Map<String, Object> result = wheelService.spinWheel(userId, wheelType);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("转盘旋转失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户转盘记录
     */
    @GetMapping("/records")
    public Result<Map<String, Object>> getUserWheelRecords(@RequestParam(defaultValue = "1") int page,
                                                          @RequestParam(defaultValue = "10") int size,
                                                          HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            // 限制每页大小
            if (size > 20) {
                size = 20;
            }
            if (size < 5) {
                size = 5;
            }
            
            Map<String, Object> result = wheelService.getUserWheelRecords(userId, page, size);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取转盘记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 清空用户转盘记录
     */
    @DeleteMapping("/records")
    public Result<String> clearUserWheelRecords(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = wheelService.clearUserWheelRecords(userId);
            
            if (success) {
                return Result.success("清空成功");
            } else {
                return Result.error("清空失败");
            }
        } catch (Exception e) {
            return Result.error("清空转盘记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 重置用户转盘配置为默认
     */
    @PostMapping("/{wheelType}/reset")
    public Result<String> resetUserWheelConfig(@PathVariable String wheelType, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = wheelService.initUserWheelConfigs(userId, wheelType);
            
            if (success) {
                return Result.success("重置成功");
            } else {
                return Result.error("重置失败");
            }
        } catch (Exception e) {
            return Result.error("重置转盘配置失败：" + e.getMessage());
        }
    }
}
