package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.entity.GameProgress;
import com.tuqiwei.entity.GameType;
import com.tuqiwei.entity.UserGameRecord;
import com.tuqiwei.entity.UserGameStats;
import com.tuqiwei.service.GameService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 游戏控制器
 */
@RestController
@RequestMapping("/api/games")
public class GameController {
    
    @Autowired
    private GameService gameService;
    
    /**
     * 获取所有游戏列表
     */
    @GetMapping("/list")
    public Result<List<GameType>> getGameList() {
        try {
            List<GameType> games = gameService.getAllActiveGames();
            return Result.success(games);
        } catch (Exception e) {
            return Result.error("获取游戏列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取游戏详情
     */
    @GetMapping("/{gameCode}")
    public Result<GameType> getGameDetail(@PathVariable String gameCode) {
        try {
            GameType game = gameService.getGameByCode(gameCode);
            if (game == null) {
                return Result.error("游戏不存在");
            }
            return Result.success(game);
        } catch (Exception e) {
            return Result.error("获取游戏详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户游戏统计
     */
    @GetMapping("/{gameCode}/stats")
    public Result<UserGameStats> getUserGameStats(@PathVariable String gameCode, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            UserGameStats stats = gameService.getUserGameStats(userId, gameCode);
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取游戏统计失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户游戏记录
     */
    @GetMapping("/{gameCode}/records")
    public Result<List<UserGameRecord>> getUserGameRecords(@PathVariable String gameCode, 
                                                          @RequestParam(defaultValue = "10") int limit,
                                                          HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<UserGameRecord> records = gameService.getUserGameRecords(userId, gameCode, limit);
            return Result.success(records);
        } catch (Exception e) {
            return Result.error("获取游戏记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 开始新游戏
     */
    @PostMapping("/{gameCode}/start")
    public Result<Map<String, Object>> startNewGame(@PathVariable String gameCode, 
                                                    @RequestBody Map<String, Object> request,
                                                    HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Integer level = Integer.valueOf(request.getOrDefault("level", 1).toString());
            Map<String, Object> result = gameService.startNewGame(userId, gameCode, level);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("开始游戏失败：" + e.getMessage());
        }
    }
    
    /**
     * 继续游戏
     */
    @PostMapping("/{gameCode}/continue")
    public Result<Map<String, Object>> continueGame(@PathVariable String gameCode, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Map<String, Object> result = gameService.continueGame(userId, gameCode);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("继续游戏失败：" + e.getMessage());
        }
    }
    
    /**
     * 暂停游戏
     */
    @PostMapping("/{gameCode}/pause")
    public Result<String> pauseGame(@PathVariable String gameCode, 
                                   @RequestBody Map<String, Object> request,
                                   HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String gameBoard = request.get("gameBoard").toString();
            Long currentScore = Long.valueOf(request.get("currentScore").toString());
            Integer playTime = Integer.valueOf(request.get("playTime").toString());
            
            boolean success = gameService.pauseGame(userId, gameCode, gameBoard, currentScore, playTime);
            
            if (success) {
                return Result.success("游戏已暂停");
            } else {
                return Result.error("暂停游戏失败");
            }
        } catch (Exception e) {
            return Result.error("暂停游戏失败：" + e.getMessage());
        }
    }
    
    /**
     * 结束游戏
     */
    @PostMapping("/{gameCode}/finish")
    public Result<Map<String, Object>> finishGame(@PathVariable String gameCode, 
                                                 @RequestBody Map<String, Object> request,
                                                 HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Long finalScore = Long.valueOf(request.get("finalScore").toString());
            String gameData = request.getOrDefault("gameData", "").toString();
            Integer playTime = Integer.valueOf(request.get("playTime").toString());
            Boolean isCompleted = Boolean.valueOf(request.getOrDefault("isCompleted", false).toString());
            
            Map<String, Object> result = gameService.finishGame(userId, gameCode, finalScore, gameData, playTime, isCompleted);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            return Result.error("结束游戏失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取游戏进度
     */
    @GetMapping("/{gameCode}/progress")
    public Result<GameProgress> getGameProgress(@PathVariable String gameCode, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            GameProgress progress = gameService.getGameProgress(userId, gameCode);
            return Result.success(progress);
        } catch (Exception e) {
            return Result.error("获取游戏进度失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户所有游戏统计
     */
    @GetMapping("/stats/all")
    public Result<List<UserGameStats>> getUserAllGameStats(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<UserGameStats> stats = gameService.getUserAllGameStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取游戏统计失败：" + e.getMessage());
        }
    }
}
