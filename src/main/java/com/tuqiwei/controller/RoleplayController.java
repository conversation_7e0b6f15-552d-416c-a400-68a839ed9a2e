package com.tuqiwei.controller;

import com.tuqiwei.common.Result;
import com.tuqiwei.entity.RoleplayCharacter;
import com.tuqiwei.entity.RoleplayMessage;
import com.tuqiwei.entity.RoleplaySession;
import com.tuqiwei.service.RoleplayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 角色扮演控制器
 */
@RestController
@RequestMapping("/api/roleplay")
public class RoleplayController {
    
    @Autowired
    private RoleplayService roleplayService;
    
    /**
     * 获取所有角色
     */
    @GetMapping("/characters")
    public Result<List<RoleplayCharacter>> getAllCharacters() {
        try {
            List<RoleplayCharacter> characters = roleplayService.getAllCharacters();
            return Result.success(characters);
        } catch (Exception e) {
            return Result.error("获取角色列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据类型获取角色
     */
    @GetMapping("/characters/type/{characterType}")
    public Result<List<RoleplayCharacter>> getCharactersByType(@PathVariable String characterType) {
        try {
            List<RoleplayCharacter> characters = roleplayService.getCharactersByType(characterType);
            return Result.success(characters);
        } catch (Exception e) {
            return Result.error("获取角色列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据分类获取角色
     */
    @GetMapping("/characters/category/{category}")
    public Result<List<RoleplayCharacter>> getCharactersByCategory(@PathVariable String category) {
        try {
            List<RoleplayCharacter> characters = roleplayService.getCharactersByCategory(category);
            return Result.success(characters);
        } catch (Exception e) {
            return Result.error("获取角色列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 搜索角色
     */
    @GetMapping("/characters/search")
    public Result<List<RoleplayCharacter>> searchCharacters(@RequestParam String keyword) {
        try {
            List<RoleplayCharacter> characters = roleplayService.searchCharacters(keyword);
            return Result.success(characters);
        } catch (Exception e) {
            return Result.error("搜索角色失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取角色详情
     */
    @GetMapping("/characters/{id}")
    public Result<RoleplayCharacter> getCharacterById(@PathVariable Long id) {
        try {
            RoleplayCharacter character = roleplayService.getCharacterById(id);
            if (character == null) {
                return Result.error("角色不存在");
            }
            return Result.success(character);
        } catch (Exception e) {
            return Result.error("获取角色详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取角色筛选条件
     */
    @GetMapping("/filters")
    public Result<Map<String, Object>> getCharacterFilters() {
        try {
            Map<String, Object> filters = roleplayService.getCharacterFilters();
            return Result.success(filters);
        } catch (Exception e) {
            return Result.error("获取筛选条件失败：" + e.getMessage());
        }
    }
    
    /**
     * 开始角色扮演会话
     */
    @PostMapping("/start")
    public Result<Map<String, Object>> startRoleplaySession(@RequestBody Map<String, Object> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            Long characterId = Long.valueOf(request.get("characterId").toString());
            Map<String, Object> result = roleplayService.startRoleplaySession(characterId, userId);
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.error(result.get("message").toString());
            }
            
        } catch (Exception e) {
            return Result.error("启动角色扮演失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户的会话列表
     */
    @GetMapping("/sessions")
    public Result<List<RoleplaySession>> getUserSessions(HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            List<RoleplaySession> sessions = roleplayService.getUserSessions(userId);
            return Result.success(sessions);
        } catch (Exception e) {
            return Result.error("获取会话列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取会话详情
     */
    @GetMapping("/sessions/{sessionId}")
    public Result<RoleplaySession> getSessionById(@PathVariable Long sessionId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            RoleplaySession roleplaySession = roleplayService.getSessionById(sessionId);
            if (roleplaySession == null || !roleplaySession.getUserId().equals(userId)) {
                return Result.error("会话不存在或无权限访问");
            }
            return Result.success(roleplaySession);
        } catch (Exception e) {
            return Result.error("获取会话详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取会话消息列表
     */
    @GetMapping("/sessions/{sessionId}/messages")
    public Result<List<RoleplayMessage>> getSessionMessages(@PathVariable Long sessionId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            // 验证会话权限
            RoleplaySession roleplaySession = roleplayService.getSessionById(sessionId);
            if (roleplaySession == null || !roleplaySession.getUserId().equals(userId)) {
                return Result.error("会话不存在或无权限访问");
            }
            
            List<RoleplayMessage> messages = roleplayService.getSessionMessages(sessionId);
            return Result.success(messages);
        } catch (Exception e) {
            return Result.error("获取消息列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 发送消息
     */
    @PostMapping("/sessions/{sessionId}/send")
    public Result<String> sendMessage(@PathVariable Long sessionId, @RequestBody Map<String, Object> request, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            // 验证会话权限
            RoleplaySession roleplaySession = roleplayService.getSessionById(sessionId);
            if (roleplaySession == null || !roleplaySession.getUserId().equals(userId)) {
                return Result.error("会话不存在或无权限访问");
            }
            
            String userMessage = request.get("message").toString();
            String reply = roleplayService.sendMessage(sessionId, userMessage, userId);
            
            return Result.success(reply);
            
        } catch (Exception e) {
            return Result.error("发送消息失败：" + e.getMessage());
        }
    }
    
    /**
     * 收藏/取消收藏消息
     */
    @PostMapping("/messages/{messageId}/favorite")
    public Result<String> toggleMessageFavorite(@PathVariable Long messageId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = roleplayService.toggleMessageFavorite(messageId, userId);
            if (success) {
                return Result.success("操作成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            return Result.error("操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除会话
     */
    @DeleteMapping("/sessions/{sessionId}")
    public Result<String> deleteSession(@PathVariable Long sessionId, HttpSession session) {
        Long userId = (Long) session.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        try {
            boolean success = roleplayService.deleteSession(sessionId, userId);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }
}
