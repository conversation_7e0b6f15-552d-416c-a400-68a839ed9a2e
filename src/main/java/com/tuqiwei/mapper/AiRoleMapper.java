package com.tuqiwei.mapper;

import com.tuqiwei.entity.AiRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI角色数据访问层
 */
@Mapper
public interface AiRoleMapper {
    
    /**
     * 插入角色
     */
    int insert(AiRole aiRole);
    
    /**
     * 根据ID查询角色
     */
    AiRole selectById(@Param("id") Long id);
    
    /**
     * 查询所有系统角色
     */
    List<AiRole> selectSystemRoles();
    
    /**
     * 根据分类查询角色
     */
    List<AiRole> selectByCategory(@Param("category") String category);
    
    /**
     * 根据用户ID查询自定义角色
     */
    List<AiRole> selectCustomRolesByUserId(@Param("userId") Long userId);
    
    /**
     * 查询热门角色（按使用次数排序）
     */
    List<AiRole> selectPopularRoles(@Param("limit") Integer limit);
    
    /**
     * 更新角色信息
     */
    int updateById(AiRole aiRole);
    
    /**
     * 增加使用次数
     */
    int incrementUsageCount(@Param("id") Long id);
    
    /**
     * 更新角色状态
     */
    int updateStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);
    
    /**
     * 删除角色
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 统计角色数量
     */
    int countByCategory(@Param("category") String category);
}
