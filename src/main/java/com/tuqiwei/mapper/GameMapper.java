package com.tuqiwei.mapper;

import com.tuqiwei.entity.GameProgress;
import com.tuqiwei.entity.GameType;
import com.tuqiwei.entity.UserGameRecord;
import com.tuqiwei.entity.UserGameStats;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 游戏相关Mapper
 */
@Mapper
public interface GameMapper {
    
    // ==================== 游戏类型相关 ====================
    
    /**
     * 获取所有活跃的游戏类型
     */
    @Select("SELECT * FROM game_types WHERE is_active = 1 ORDER BY sort_order ASC, id ASC")
    List<GameType> selectActiveGameTypes();
    
    /**
     * 根据游戏代码获取游戏类型
     */
    @Select("SELECT * FROM game_types WHERE game_code = #{gameCode} AND is_active = 1")
    GameType selectGameTypeByCode(String gameCode);
    
    // ==================== 用户游戏记录相关 ====================
    
    /**
     * 插入游戏记录
     */
    @Insert("INSERT INTO user_game_records (user_id, game_code, score, level, game_data, play_time, is_completed) " +
            "VALUES (#{userId}, #{gameCode}, #{score}, #{level}, #{gameData}, #{playTime}, #{isCompleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertGameRecord(UserGameRecord record);
    
    /**
     * 获取用户某个游戏的记录列表
     */
    @Select("SELECT * FROM user_game_records WHERE user_id = #{userId} AND game_code = #{gameCode} " +
            "ORDER BY score DESC, created_time DESC LIMIT #{limit}")
    List<UserGameRecord> selectUserGameRecords(@Param("userId") Long userId, 
                                               @Param("gameCode") String gameCode, 
                                               @Param("limit") int limit);
    
    // ==================== 用户游戏统计相关 ====================
    
    /**
     * 获取用户游戏统计
     */
    @Select("SELECT * FROM user_game_stats WHERE user_id = #{userId} AND game_code = #{gameCode}")
    UserGameStats selectUserGameStats(@Param("userId") Long userId, @Param("gameCode") String gameCode);
    
    /**
     * 插入用户游戏统计
     */
    @Insert("INSERT INTO user_game_stats (user_id, game_code, total_games, best_score, second_score, third_score, " +
            "total_play_time, last_play_time, game_settings) VALUES (#{userId}, #{gameCode}, #{totalGames}, " +
            "#{bestScore}, #{secondScore}, #{thirdScore}, #{totalPlayTime}, #{lastPlayTime}, #{gameSettings})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUserGameStats(UserGameStats stats);
    
    /**
     * 更新用户游戏统计
     */
    @Update("UPDATE user_game_stats SET total_games = #{totalGames}, best_score = #{bestScore}, " +
            "second_score = #{secondScore}, third_score = #{thirdScore}, total_play_time = #{totalPlayTime}, " +
            "last_play_time = #{lastPlayTime}, game_settings = #{gameSettings} " +
            "WHERE user_id = #{userId} AND game_code = #{gameCode}")
    int updateUserGameStats(UserGameStats stats);
    
    // ==================== 游戏进度相关 ====================
    
    /**
     * 获取用户游戏进度
     */
    @Select("SELECT * FROM game_progress WHERE user_id = #{userId} AND game_code = #{gameCode}")
    GameProgress selectGameProgress(@Param("userId") Long userId, @Param("gameCode") String gameCode);
    
    /**
     * 插入游戏进度
     */
    @Insert("INSERT INTO game_progress (user_id, game_code, current_score, game_board, game_level, " +
            "play_time, is_paused) VALUES (#{userId}, #{gameCode}, #{currentScore}, #{gameBoard}, " +
            "#{gameLevel}, #{playTime}, #{isPaused})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertGameProgress(GameProgress progress);
    
    /**
     * 更新游戏进度
     */
    @Update("UPDATE game_progress SET current_score = #{currentScore}, game_board = #{gameBoard}, " +
            "game_level = #{gameLevel}, play_time = #{playTime}, is_paused = #{isPaused} " +
            "WHERE user_id = #{userId} AND game_code = #{gameCode}")
    int updateGameProgress(GameProgress progress);
    
    /**
     * 删除游戏进度
     */
    @Delete("DELETE FROM game_progress WHERE user_id = #{userId} AND game_code = #{gameCode}")
    int deleteGameProgress(@Param("userId") Long userId, @Param("gameCode") String gameCode);
    
    /**
     * 获取用户所有游戏统计
     */
    @Select("SELECT * FROM user_game_stats WHERE user_id = #{userId} ORDER BY last_play_time DESC")
    List<UserGameStats> selectUserAllGameStats(Long userId);
}
