package com.tuqiwei.mapper;

import com.tuqiwei.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMapper {
    
    /**
     * 根据用户名查询用户
     */
    User selectByUsername(@Param("username") String username);
    
    /**
     * 根据ID查询用户
     */
    User selectById(@Param("id") Long id);
    
    /**
     * 更新用户信息
     */
    int updateUser(User user);
    
    /**
     * 更新用户密码
     */
    int updatePassword(@Param("id") Long id, @Param("password") String password);
    
    /**
     * 更新用户头像
     */
    int updateAvatar(@Param("id") Long id, @Param("avatar") String avatar);
}
