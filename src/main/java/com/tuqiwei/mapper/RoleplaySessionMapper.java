package com.tuqiwei.mapper;

import com.tuqiwei.entity.RoleplaySession;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 角色扮演会话Mapper
 */
@Mapper
public interface RoleplaySessionMapper {
    
    /**
     * 插入会话
     */
    @Insert("INSERT INTO roleplay_sessions (user_id, character_id, session_name, session_context, " +
            "is_active, message_count) VALUES (#{userId}, #{characterId}, #{sessionName}, " +
            "#{sessionContext}, #{isActive}, #{messageCount})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(RoleplaySession session);
    
    /**
     * 根据ID查询会话
     */
    @Select("SELECT rs.*, rc.character_name, rc.avatar_url, rc.character_type, rc.category " +
            "FROM roleplay_sessions rs " +
            "LEFT JOIN roleplay_characters rc ON rs.character_id = rc.id " +
            "WHERE rs.id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "characterId", column = "character_id"),
        @Result(property = "sessionName", column = "session_name"),
        @Result(property = "sessionContext", column = "session_context"),
        @Result(property = "isActive", column = "is_active"),
        @Result(property = "messageCount", column = "message_count"),
        @Result(property = "lastMessageTime", column = "last_message_time"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time"),
        @Result(property = "character.characterName", column = "character_name"),
        @Result(property = "character.avatarUrl", column = "avatar_url"),
        @Result(property = "character.characterType", column = "character_type"),
        @Result(property = "character.category", column = "category")
    })
    RoleplaySession selectById(Long id);
    
    /**
     * 查询用户的会话列表
     */
    @Select("SELECT rs.*, rc.character_name, rc.avatar_url, rc.character_type, rc.category " +
            "FROM roleplay_sessions rs " +
            "LEFT JOIN roleplay_characters rc ON rs.character_id = rc.id " +
            "WHERE rs.user_id = #{userId} AND rs.is_active = 1 " +
            "ORDER BY rs.last_message_time DESC, rs.created_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "characterId", column = "character_id"),
        @Result(property = "sessionName", column = "session_name"),
        @Result(property = "sessionContext", column = "session_context"),
        @Result(property = "isActive", column = "is_active"),
        @Result(property = "messageCount", column = "message_count"),
        @Result(property = "lastMessageTime", column = "last_message_time"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time"),
        @Result(property = "character.characterName", column = "character_name"),
        @Result(property = "character.avatarUrl", column = "avatar_url"),
        @Result(property = "character.characterType", column = "character_type"),
        @Result(property = "character.category", column = "category")
    })
    List<RoleplaySession> selectByUserId(Long userId);
    
    /**
     * 更新会话信息
     */
    @Update("UPDATE roleplay_sessions SET session_name = #{sessionName}, session_context = #{sessionContext}, " +
            "message_count = #{messageCount}, last_message_time = #{lastMessageTime} WHERE id = #{id}")
    int update(RoleplaySession session);
    
    /**
     * 增加消息计数
     */
    @Update("UPDATE roleplay_sessions SET message_count = message_count + 1, last_message_time = NOW() WHERE id = #{sessionId}")
    int incrementMessageCount(Long sessionId);
    
    /**
     * 删除会话
     */
    @Delete("DELETE FROM roleplay_sessions WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 检查用户是否有该角色的活跃会话
     */
    @Select("SELECT COUNT(*) FROM roleplay_sessions WHERE user_id = #{userId} AND character_id = #{characterId} AND is_active = 1")
    int countActiveSessionsByUserAndCharacter(@Param("userId") Long userId, @Param("characterId") Long characterId);
}
