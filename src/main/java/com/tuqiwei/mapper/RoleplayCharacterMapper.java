package com.tuqiwei.mapper;

import com.tuqiwei.entity.RoleplayCharacter;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 角色扮演角色Mapper
 */
@Mapper
public interface RoleplayCharacterMapper {
    
    /**
     * 插入角色
     */
    @Insert("INSERT INTO roleplay_characters (character_name, character_type, category, avatar_url, description, " +
            "personality, background_story, speaking_style, system_prompt, greeting_message, example_dialogues, " +
            "tags, is_active, is_public, creator_id, usage_count, rating) " +
            "VALUES (#{characterName}, #{characterType}, #{category}, #{avatarUrl}, #{description}, " +
            "#{personality}, #{backgroundStory}, #{speakingStyle}, #{systemPrompt}, #{greetingMessage}, " +
            "#{exampleDialogues}, #{tags}, #{isActive}, #{isPublic}, #{creatorId}, #{usageCount}, #{rating})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(RoleplayCharacter character);
    
    /**
     * 根据ID查询角色
     */
    @Select("SELECT * FROM roleplay_characters WHERE id = #{id}")
    RoleplayCharacter selectById(Long id);
    
    /**
     * 查询所有活跃的公开角色
     */
    @Select("SELECT * FROM roleplay_characters WHERE is_active = 1 AND is_public = 1 ORDER BY usage_count DESC, rating DESC")
    List<RoleplayCharacter> selectActivePublicCharacters();
    
    /**
     * 根据类型查询角色
     */
    @Select("SELECT * FROM roleplay_characters WHERE character_type = #{characterType} AND is_active = 1 AND is_public = 1 ORDER BY usage_count DESC")
    List<RoleplayCharacter> selectByType(String characterType);
    
    /**
     * 根据分类查询角色
     */
    @Select("SELECT * FROM roleplay_characters WHERE category = #{category} AND is_active = 1 AND is_public = 1 ORDER BY usage_count DESC")
    List<RoleplayCharacter> selectByCategory(String category);
    
    /**
     * 搜索角色
     */
    @Select("SELECT * FROM roleplay_characters WHERE (character_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR description LIKE CONCAT('%', #{keyword}, '%') OR tags LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND is_active = 1 AND is_public = 1 ORDER BY usage_count DESC")
    List<RoleplayCharacter> searchCharacters(String keyword);
    
    /**
     * 更新使用次数
     */
    @Update("UPDATE roleplay_characters SET usage_count = usage_count + 1 WHERE id = #{id}")
    int incrementUsageCount(Long id);
    
    /**
     * 更新角色信息
     */
    @Update("UPDATE roleplay_characters SET character_name = #{characterName}, character_type = #{characterType}, " +
            "category = #{category}, avatar_url = #{avatarUrl}, description = #{description}, " +
            "personality = #{personality}, background_story = #{backgroundStory}, speaking_style = #{speakingStyle}, " +
            "system_prompt = #{systemPrompt}, greeting_message = #{greetingMessage}, " +
            "example_dialogues = #{exampleDialogues}, tags = #{tags}, is_active = #{isActive}, " +
            "is_public = #{isPublic} WHERE id = #{id}")
    int update(RoleplayCharacter character);
    
    /**
     * 删除角色
     */
    @Delete("DELETE FROM roleplay_characters WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 获取角色类型列表
     */
    @Select("SELECT DISTINCT character_type FROM roleplay_characters WHERE is_active = 1 AND is_public = 1")
    List<String> selectCharacterTypes();
    
    /**
     * 获取角色分类列表
     */
    @Select("SELECT DISTINCT category FROM roleplay_characters WHERE is_active = 1 AND is_public = 1")
    List<String> selectCategories();
}
