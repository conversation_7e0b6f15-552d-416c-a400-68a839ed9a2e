package com.tuqiwei.mapper;

import com.tuqiwei.entity.ChatMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天消息数据访问层
 */
@Mapper
public interface ChatMessageMapper {
    
    /**
     * 插入消息
     */
    int insert(ChatMessage chatMessage);
    
    /**
     * 根据ID查询消息
     */
    ChatMessage selectById(@Param("id") Long id);
    
    /**
     * 根据会话ID查询消息列表
     */
    List<ChatMessage> selectBySessionId(@Param("sessionId") Long sessionId);
    
    /**
     * 根据会话ID分页查询消息列表
     */
    List<ChatMessage> selectBySessionIdWithPage(@Param("sessionId") Long sessionId, 
                                               @Param("offset") Integer offset, 
                                               @Param("limit") Integer limit);
    
    /**
     * 根据用户ID查询收藏的消息
     */
    List<ChatMessage> selectFavoritesByUserId(@Param("userId") Long userId);
    
    /**
     * 更新消息状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);
    
    /**
     * 更新消息收藏状态
     */
    int updateFavorite(@Param("id") Long id, @Param("isFavorite") Boolean isFavorite);
    
    /**
     * 删除消息
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据会话ID删除所有消息
     */
    int deleteBySessionId(@Param("sessionId") Long sessionId);
    
    /**
     * 统计会话消息数量
     */
    int countBySessionId(@Param("sessionId") Long sessionId);
    
    /**
     * 统计用户今日消息数量
     */
    int countTodayByUserId(@Param("userId") Long userId);
    
    /**
     * 获取会话的最后一条消息
     */
    ChatMessage selectLastMessageBySessionId(@Param("sessionId") Long sessionId);
}
