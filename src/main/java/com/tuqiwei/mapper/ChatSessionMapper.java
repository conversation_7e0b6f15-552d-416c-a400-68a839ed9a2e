package com.tuqiwei.mapper;

import com.tuqiwei.entity.ChatSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天会话数据访问层
 */
@Mapper
public interface ChatSessionMapper {
    
    /**
     * 创建新会话
     */
    int insert(ChatSession chatSession);
    
    /**
     * 根据ID查询会话
     */
    ChatSession selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询会话列表
     */
    List<ChatSession> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID和会话类型查询会话列表
     */
    List<ChatSession> selectByUserIdAndType(@Param("userId") Long userId, @Param("sessionType") String sessionType);
    
    /**
     * 更新会话信息
     */
    int updateById(ChatSession chatSession);
    
    /**
     * 更新会话名称
     */
    int updateSessionName(@Param("id") Long id, @Param("sessionName") String sessionName);
    
    /**
     * 软删除会话
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 物理删除会话
     */
    int physicalDeleteById(@Param("id") Long id);
    
    /**
     * 统计用户会话数量
     */
    int countByUserId(@Param("userId") Long userId);
    
    /**
     * 统计用户今日会话数量
     */
    int countTodayByUserId(@Param("userId") Long userId);
}
