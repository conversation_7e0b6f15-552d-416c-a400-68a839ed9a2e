package com.tuqiwei.mapper;

import com.tuqiwei.entity.WheelConfig;
import com.tuqiwei.entity.WheelRecord;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 转盘数据访问层
 */
@Mapper
public interface WheelMapper {

    // ==================== 转盘配置相关 ====================
    
    /**
     * 获取用户转盘配置
     */
    @Select("SELECT * FROM wheel_configs WHERE user_id = #{userId} AND wheel_type = #{wheelType} AND is_active = 1 ORDER BY slot_index")
    List<WheelConfig> selectUserWheelConfigs(@Param("userId") Long userId, @Param("wheelType") String wheelType);
    
    /**
     * 获取默认转盘配置
     */
    @Select("SELECT * FROM wheel_configs WHERE user_id = 0 AND wheel_type = #{wheelType} AND is_active = 1 ORDER BY slot_index")
    List<WheelConfig> selectDefaultWheelConfigs(@Param("wheelType") String wheelType);
    
    /**
     * 插入转盘配置
     */
    @Insert("INSERT INTO wheel_configs (user_id, wheel_type, slot_index, content, weight, is_active) " +
            "VALUES (#{userId}, #{wheelType}, #{slotIndex}, #{content}, #{weight}, #{isActive})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertWheelConfig(WheelConfig config);
    
    /**
     * 更新转盘配置
     */
    @Update("UPDATE wheel_configs SET content = #{content}, weight = #{weight}, is_active = #{isActive} " +
            "WHERE user_id = #{userId} AND wheel_type = #{wheelType} AND slot_index = #{slotIndex}")
    int updateWheelConfig(WheelConfig config);
    
    /**
     * 删除用户转盘配置
     */
    @Delete("DELETE FROM wheel_configs WHERE user_id = #{userId} AND wheel_type = #{wheelType}")
    int deleteUserWheelConfigs(@Param("userId") Long userId, @Param("wheelType") String wheelType);
    
    /**
     * 批量插入转盘配置
     */
    @Insert("<script>" +
            "INSERT INTO wheel_configs (user_id, wheel_type, slot_index, content, weight, is_active) VALUES " +
            "<foreach collection='configs' item='config' separator=','>" +
            "(#{config.userId}, #{config.wheelType}, #{config.slotIndex}, #{config.content}, #{config.weight}, #{config.isActive})" +
            "</foreach>" +
            "</script>")
    int batchInsertWheelConfigs(@Param("configs") List<WheelConfig> configs);

    // ==================== 转盘记录相关 ====================
    
    /**
     * 插入转盘记录
     */
    @Insert("INSERT INTO wheel_records (user_id, wheel_type, result_content, spin_time) " +
            "VALUES (#{userId}, #{wheelType}, #{resultContent}, #{spinTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertWheelRecord(WheelRecord record);
    
    /**
     * 获取用户转盘记录（分页）
     */
    @Select("SELECT * FROM wheel_records WHERE user_id = #{userId} " +
            "ORDER BY spin_time DESC LIMIT #{offset}, #{limit}")
    List<WheelRecord> selectUserWheelRecords(@Param("userId") Long userId, 
                                           @Param("offset") int offset, 
                                           @Param("limit") int limit);
    
    /**
     * 获取用户转盘记录总数
     */
    @Select("SELECT COUNT(*) FROM wheel_records WHERE user_id = #{userId}")
    int countUserWheelRecords(@Param("userId") Long userId);
    
    /**
     * 清空用户转盘记录
     */
    @Delete("DELETE FROM wheel_records WHERE user_id = #{userId}")
    int clearUserWheelRecords(@Param("userId") Long userId);
    
    /**
     * 删除用户最早的记录（保持记录数量限制）
     */
    @Delete("DELETE FROM wheel_records WHERE user_id = #{userId} AND id IN (" +
            "SELECT id FROM (SELECT id FROM wheel_records WHERE user_id = #{userId} " +
            "ORDER BY spin_time ASC LIMIT #{deleteCount}) AS temp)")
    int deleteOldestRecords(@Param("userId") Long userId, @Param("deleteCount") int deleteCount);
    
    /**
     * 删除过期记录（7天前）
     */
    @Delete("DELETE FROM wheel_records WHERE spin_time < DATE_SUB(NOW(), INTERVAL 7 DAY)")
    int deleteExpiredRecords();
}
