package com.tuqiwei.mapper;

import com.tuqiwei.entity.MoodRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 心情记录数据访问层
 */
@Mapper
public interface MoodRecordMapper {
    
    /**
     * 插入心情记录
     */
    int insert(MoodRecord moodRecord);
    
    /**
     * 根据用户ID查询心情记录
     */
    List<MoodRecord> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据会话ID查询心情记录
     */
    List<MoodRecord> selectBySessionId(@Param("sessionId") Long sessionId);
    
    /**
     * 查询用户最近的心情记录
     */
    MoodRecord selectLatestByUserId(@Param("userId") Long userId);
    
    /**
     * 根据时间范围查询用户心情记录
     */
    List<MoodRecord> selectByUserIdAndTimeRange(@Param("userId") Long userId, 
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计用户各种心情类型的次数
     */
    List<MoodRecord> selectMoodStatsByUserId(@Param("userId") Long userId);
    
    /**
     * 删除心情记录
     */
    int deleteById(@Param("id") Long id);
}
