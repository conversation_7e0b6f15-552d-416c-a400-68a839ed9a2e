package com.tuqiwei.mapper;

import com.tuqiwei.entity.RoleplayMessage;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 角色扮演消息Mapper
 */
@Mapper
public interface RoleplayMessageMapper {
    
    /**
     * 插入消息
     */
    @Insert("INSERT INTO roleplay_messages (session_id, user_id, role, content, emotion, is_favorite) " +
            "VALUES (#{sessionId}, #{userId}, #{role}, #{content}, #{emotion}, #{isFavorite})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(RoleplayMessage message);
    
    /**
     * 根据会话ID查询消息列表
     */
    @Select("SELECT * FROM roleplay_messages WHERE session_id = #{sessionId} ORDER BY created_time ASC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "sessionId", column = "session_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "role", column = "role"),
        @Result(property = "content", column = "content"),
        @Result(property = "emotion", column = "emotion"),
        @Result(property = "isFavorite", column = "is_favorite"),
        @Result(property = "createdTime", column = "created_time")
    })
    List<RoleplayMessage> selectBySessionId(Long sessionId);
    
    /**
     * 根据ID查询消息
     */
    @Select("SELECT * FROM roleplay_messages WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "sessionId", column = "session_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "role", column = "role"),
        @Result(property = "content", column = "content"),
        @Result(property = "emotion", column = "emotion"),
        @Result(property = "isFavorite", column = "is_favorite"),
        @Result(property = "createdTime", column = "created_time")
    })
    RoleplayMessage selectById(Long id);
    
    /**
     * 更新消息收藏状态
     */
    @Update("UPDATE roleplay_messages SET is_favorite = #{isFavorite} WHERE id = #{id}")
    int updateFavoriteStatus(@Param("id") Long id, @Param("isFavorite") Boolean isFavorite);
    
    /**
     * 删除消息
     */
    @Delete("DELETE FROM roleplay_messages WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 删除会话的所有消息
     */
    @Delete("DELETE FROM roleplay_messages WHERE session_id = #{sessionId}")
    int deleteBySessionId(Long sessionId);
    
    /**
     * 获取会话的最近几条消息（用于上下文）
     */
    @Select("SELECT * FROM roleplay_messages WHERE session_id = #{sessionId} ORDER BY created_time DESC LIMIT #{limit}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "sessionId", column = "session_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "role", column = "role"),
        @Result(property = "content", column = "content"),
        @Result(property = "emotion", column = "emotion"),
        @Result(property = "isFavorite", column = "is_favorite"),
        @Result(property = "createdTime", column = "created_time")
    })
    List<RoleplayMessage> selectRecentMessages(@Param("sessionId") Long sessionId, @Param("limit") int limit);
    
    /**
     * 统计会话消息数量
     */
    @Select("SELECT COUNT(*) FROM roleplay_messages WHERE session_id = #{sessionId}")
    int countBySessionId(Long sessionId);
}
