package com.tuqiwei.mapper;

import com.tuqiwei.entity.ComfortScenario;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 哄人场景模板数据访问层
 */
@Mapper
public interface ComfortScenarioMapper {
    
    /**
     * 查询所有启用的场景
     */
    List<ComfortScenario> selectActiveScenarios();
    
    /**
     * 根据场景类型查询
     */
    List<ComfortScenario> selectByScenarioType(@Param("scenarioType") String scenarioType);
    
    /**
     * 根据心情类型查询适合的场景
     */
    List<ComfortScenario> selectByMoodType(@Param("moodType") String moodType);
    
    /**
     * 根据ID查询场景
     */
    ComfortScenario selectById(@Param("id") Long id);
    
    /**
     * 增加使用次数
     */
    int incrementUsageCount(@Param("id") Long id);
    
    /**
     * 插入新场景
     */
    int insert(ComfortScenario scenario);
    
    /**
     * 更新场景
     */
    int updateById(ComfortScenario scenario);
    
    /**
     * 删除场景
     */
    int deleteById(@Param("id") Long id);
}
