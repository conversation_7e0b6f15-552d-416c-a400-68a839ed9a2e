package com.tuqiwei.mapper;

import com.tuqiwei.entity.AdminUser;
import com.tuqiwei.entity.User;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 管理员数据访问层
 */
@Mapper
public interface AdminMapper {

    // ==================== 管理员认证相关 ====================
    
    /**
     * 根据用户名查询管理员
     */
    @Select("SELECT * FROM admin_users WHERE username = #{username}")
    AdminUser selectByUsername(@Param("username") String username);
    
    /**
     * 更新管理员最后登录时间
     */
    @Update("UPDATE admin_users SET last_login_time = NOW() WHERE id = #{adminId}")
    int updateLastLoginTime(@Param("adminId") Long adminId);

    // ==================== 用户管理相关 ====================
    
    /**
     * 分页查询用户列表
     */
    @Select("<script>" +
            "SELECT * FROM user WHERE 1=1 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (username LIKE CONCAT('%', #{keyword}, '%') OR nickname LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    List<User> selectUserList(@Param("keyword") String keyword, 
                             @Param("status") Boolean status,
                             @Param("offset") int offset, 
                             @Param("limit") int limit);
    
    /**
     * 查询用户总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM user WHERE 1=1 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (username LIKE CONCAT('%', #{keyword}, '%') OR nickname LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "</script>")
    int countUsers(@Param("keyword") String keyword, @Param("status") Boolean status);
    
    /**
     * 更新用户状态
     */
    @Update("UPDATE user SET status = #{status} WHERE id = #{userId}")
    int updateUserStatus(@Param("userId") Long userId, @Param("status") Boolean status);

    // ==================== 数据统计相关 ====================
    
    /**
     * 获取用户统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as totalUsers, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as activeUsers, " +
            "COUNT(CASE WHEN DATE(created_time) = CURDATE() THEN 1 END) as todayNewUsers, " +
            "COUNT(CASE WHEN DATE(last_login_time) = CURDATE() THEN 1 END) as todayActiveUsers " +
            "FROM users")
    Map<String, Object> getUserStats();
    
    /**
     * 获取聊天统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as totalSessions, " +
            "COUNT(CASE WHEN DATE(created_time) = CURDATE() THEN 1 END) as todaySessions, " +
            "(SELECT COUNT(*) FROM chat_messages) as totalMessages, " +
            "(SELECT COUNT(*) FROM chat_messages WHERE DATE(created_time) = CURDATE()) as todayMessages " +
            "FROM chat_sessions")
    Map<String, Object> getChatStats();
    
    /**
     * 获取游戏统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as totalRecords, " +
            "COUNT(CASE WHEN DATE(created_time) = CURDATE() THEN 1 END) as todayRecords, " +
            "COUNT(DISTINCT user_id) as totalPlayers, " +
            "COUNT(DISTINCT CASE WHEN DATE(created_time) = CURDATE() THEN user_id END) as todayPlayers " +
            "FROM user_game_records")
    Map<String, Object> getGameStats();
    
    /**
     * 获取最近7天用户注册趋势
     */
    @Select("SELECT DATE(created_time) as date, COUNT(*) as count " +
            "FROM user " +
            "WHERE created_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) " +
            "GROUP BY DATE(created_time) " +
            "ORDER BY date")
    List<Map<String, Object>> getUserTrend();
    
    /**
     * 获取最近7天活跃用户趋势
     */
    @Select("SELECT DATE(last_login_time) as date, COUNT(*) as count " +
            "FROM user " +
            "WHERE last_login_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) " +
            "GROUP BY DATE(last_login_time) " +
            "ORDER BY date")
    List<Map<String, Object>> getActiveUserTrend();
}
