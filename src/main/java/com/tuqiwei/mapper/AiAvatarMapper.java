package com.tuqiwei.mapper;

import com.tuqiwei.entity.AiAvatar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI头像数据访问层
 */
@Mapper
public interface AiAvatarMapper {
    
    /**
     * 插入AI头像
     */
    int insert(AiAvatar aiAvatar);
    
    /**
     * 根据用户ID查询AI头像列表
     */
    List<AiAvatar> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID获取默认AI头像
     */
    AiAvatar selectDefaultByUserId(@Param("userId") Long userId);
    
    /**
     * 设置默认头像
     */
    int setDefaultAvatar(@Param("userId") Long userId, @Param("avatarId") Long avatarId);
    
    /**
     * 取消所有默认头像
     */
    int clearDefaultAvatars(@Param("userId") Long userId);
    
    /**
     * 删除头像
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 更新头像信息
     */
    int updateById(AiAvatar aiAvatar);
}
