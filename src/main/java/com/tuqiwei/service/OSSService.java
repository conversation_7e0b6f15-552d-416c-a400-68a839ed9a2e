package com.tuqiwei.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.tuqiwei.config.OSSConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

@Slf4j
@Service
public class OSSService {

    @Autowired
    private OSSConfig ossConfig;

    /**
     * 上传文件到阿里云OSS
     */
    public String uploadFile(MultipartFile file) throws IOException {
        // 创建OSS客户端
        OSS ossClient = new OSSClientBuilder().build(
                "https://" + ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret()
        );

        try {
            // 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            // 生成唯一文件名
            String fileName = "avatar/" + UUID.randomUUID().toString() + extension;

            // 上传文件
            ossClient.putObject(ossConfig.getBucketName(), fileName, file.getInputStream());

            // 返回文件访问URL
            String fileUrl = "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint() + "/" + fileName;
            
            log.info("文件上传成功，URL: {}", fileUrl);
            return fileUrl;

        } finally {
            // 关闭OSS客户端
            ossClient.shutdown();
        }
    }
}
