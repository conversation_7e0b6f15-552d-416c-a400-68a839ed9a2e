package com.tuqiwei.service;

import com.tuqiwei.entity.RoleplayCharacter;
import com.tuqiwei.entity.RoleplayMessage;
import com.tuqiwei.entity.RoleplaySession;

import java.util.List;
import java.util.Map;

/**
 * 角色扮演服务接口
 */
public interface RoleplayService {
    
    /**
     * 获取所有活跃的公开角色
     */
    List<RoleplayCharacter> getAllCharacters();
    
    /**
     * 根据类型获取角色
     */
    List<RoleplayCharacter> getCharactersByType(String characterType);
    
    /**
     * 根据分类获取角色
     */
    List<RoleplayCharacter> getCharactersByCategory(String category);
    
    /**
     * 搜索角色
     */
    List<RoleplayCharacter> searchCharacters(String keyword);
    
    /**
     * 根据ID获取角色详情
     */
    RoleplayCharacter getCharacterById(Long id);
    
    /**
     * 获取角色类型和分类
     */
    Map<String, Object> getCharacterFilters();
    
    /**
     * 创建角色扮演会话
     */
    RoleplaySession createSession(Long userId, Long characterId, String sessionName, String sessionContext);
    
    /**
     * 获取用户的会话列表
     */
    List<RoleplaySession> getUserSessions(Long userId);
    
    /**
     * 根据ID获取会话详情
     */
    RoleplaySession getSessionById(Long sessionId);
    
    /**
     * 获取会话消息列表
     */
    List<RoleplayMessage> getSessionMessages(Long sessionId);
    
    /**
     * 发送消息并获取角色回复
     */
    String sendMessage(Long sessionId, String userMessage, Long userId);
    
    /**
     * 开始角色扮演会话
     */
    Map<String, Object> startRoleplaySession(Long characterId, Long userId);
    
    /**
     * 收藏/取消收藏消息
     */
    boolean toggleMessageFavorite(Long messageId, Long userId);
    
    /**
     * 删除会话
     */
    boolean deleteSession(Long sessionId, Long userId);
}
