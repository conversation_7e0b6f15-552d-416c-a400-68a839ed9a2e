package com.tuqiwei.service;

import com.tuqiwei.entity.GameProgress;
import com.tuqiwei.entity.GameType;
import com.tuqiwei.entity.UserGameRecord;
import com.tuqiwei.entity.UserGameStats;

import java.util.List;
import java.util.Map;

/**
 * 游戏服务接口
 */
public interface GameService {
    
    /**
     * 获取所有活跃的游戏类型
     */
    List<GameType> getAllActiveGames();
    
    /**
     * 根据游戏代码获取游戏信息
     */
    GameType getGameByCode(String gameCode);
    
    /**
     * 获取用户游戏统计
     */
    UserGameStats getUserGameStats(Long userId, String gameCode);
    
    /**
     * 保存游戏记录
     */
    boolean saveGameRecord(UserGameRecord record);
    
    /**
     * 更新用户游戏统计
     */
    boolean updateUserGameStats(Long userId, String gameCode, Long score, Integer playTime);
    
    /**
     * 获取用户游戏记录（前N条）
     */
    List<UserGameRecord> getUserGameRecords(Long userId, String gameCode, int limit);
    
    /**
     * 保存游戏进度
     */
    boolean saveGameProgress(GameProgress progress);
    
    /**
     * 获取游戏进度
     */
    GameProgress getGameProgress(Long userId, String gameCode);
    
    /**
     * 删除游戏进度
     */
    boolean deleteGameProgress(Long userId, String gameCode);
    
    /**
     * 开始新游戏
     */
    Map<String, Object> startNewGame(Long userId, String gameCode, Integer level);
    
    /**
     * 继续游戏
     */
    Map<String, Object> continueGame(Long userId, String gameCode);
    
    /**
     * 暂停游戏
     */
    boolean pauseGame(Long userId, String gameCode, String gameBoard, Long currentScore, Integer playTime);
    
    /**
     * 结束游戏
     */
    Map<String, Object> finishGame(Long userId, String gameCode, Long finalScore, String gameData, Integer playTime, Boolean isCompleted);
    
    /**
     * 获取用户所有游戏统计
     */
    List<UserGameStats> getUserAllGameStats(Long userId);
}
