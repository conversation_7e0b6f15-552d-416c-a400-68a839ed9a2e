package com.tuqiwei.service;

import com.tuqiwei.entity.User;

public interface UserService {
    
    /**
     * 用户登录
     */
    User login(String username, String password);
    
    /**
     * 根据ID获取用户信息
     */
    User getUserById(Long id);
    
    /**
     * 更新用户信息
     */
    boolean updateUser(User user);
    
    /**
     * 修改密码
     */
    boolean updatePassword(Long id, String oldPassword, String newPassword);
    
    /**
     * 更新头像
     */
    boolean updateAvatar(Long id, String avatar);
}
