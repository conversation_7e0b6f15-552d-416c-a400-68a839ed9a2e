package com.tuqiwei.service;

import com.tuqiwei.entity.ChatMessage;
import com.tuqiwei.entity.ChatSession;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * AI聊天服务接口
 */
public interface AiChatService {
    
    /**
     * 发送消息并获取AI回复
     * @param sessionId 会话ID
     * @param userMessage 用户消息
     * @param userId 用户ID
     * @return AI回复的流式响应
     */
    Flux<String> sendMessage(Long sessionId, String userMessage, Long userId);
    
    /**
     * 发送消息并获取AI回复（非流式）
     * @param sessionId 会话ID
     * @param userMessage 用户消息
     * @param userId 用户ID
     * @return AI回复内容
     */
    String sendMessageSync(Long sessionId, String userMessage, Long userId);
    
    /**
     * 创建新的聊天会话
     * @param userId 用户ID
     * @param sessionName 会话名称
     * @param sessionType 会话类型
     * @return 创建的会话
     */
    ChatSession createSession(Long userId, String sessionName, String sessionType);
    
    /**
     * 获取用户的会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    List<ChatSession> getUserSessions(Long userId);
    
    /**
     * 获取会话的消息历史
     * @param sessionId 会话ID
     * @return 消息列表
     */
    List<ChatMessage> getSessionMessages(Long sessionId);
    
    /**
     * 删除会话
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteSession(Long sessionId, Long userId);
    
    /**
     * 重命名会话
     * @param sessionId 会话ID
     * @param newName 新名称
     * @param userId 用户ID
     * @return 是否重命名成功
     */
    boolean renameSession(Long sessionId, String newName, Long userId);
    
    /**
     * 收藏/取消收藏消息
     * @param messageId 消息ID
     * @param userId 用户ID
     * @param isFavorite 是否收藏
     * @return 是否操作成功
     */
    boolean toggleMessageFavorite(Long messageId, Long userId, Boolean isFavorite);
    
    /**
     * 获取用户收藏的消息
     * @param userId 用户ID
     * @return 收藏的消息列表
     */
    List<ChatMessage> getFavoriteMessages(Long userId);
}
