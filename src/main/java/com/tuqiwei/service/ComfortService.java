package com.tuqiwei.service;

import com.tuqiwei.entity.ComfortScenario;
import com.tuqiwei.entity.MoodRecord;

import java.util.List;
import java.util.Map;

/**
 * 哄哄模拟器服务接口
 */
public interface ComfortService {
    
    /**
     * 获取所有可用的安慰场景
     */
    List<ComfortScenario> getAvailableScenarios();
    
    /**
     * 根据心情类型推荐场景
     */
    List<ComfortScenario> recommendScenarios(String moodType);
    
    /**
     * 分析用户情绪并返回建议的心情类型
     */
    String analyzeMood(String userMessage);
    
    /**
     * 记录用户心情
     */
    void recordMood(Long userId, Long sessionId, String moodType, Integer moodLevel, 
                   String description, String triggerEvent);
    
    /**
     * 获取用户心情历史
     */
    List<MoodRecord> getUserMoodHistory(Long userId);
    
    /**
     * 获取用户心情统计
     */
    Map<String, Object> getUserMoodStats(Long userId);
    
    /**
     * 生成安慰回复
     */
    String generateComfortReply(Long scenarioId, String userMessage, String moodType);
    
    /**
     * 获取场景的问候语
     */
    String getScenarioGreeting(Long scenarioId);
    
    /**
     * 更新场景使用次数
     */
    void updateScenarioUsage(Long scenarioId);
}
