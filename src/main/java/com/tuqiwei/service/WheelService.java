package com.tuqiwei.service;

import com.tuqiwei.entity.WheelConfig;
import com.tuqiwei.entity.WheelRecord;

import java.util.List;
import java.util.Map;

/**
 * 转盘服务接口
 */
public interface WheelService {
    
    /**
     * 获取用户转盘配置
     * @param userId 用户ID
     * @param wheelType 转盘类型
     * @return 转盘配置列表
     */
    List<WheelConfig> getUserWheelConfigs(Long userId, String wheelType);
    
    /**
     * 保存用户转盘配置
     * @param userId 用户ID
     * @param wheelType 转盘类型
     * @param configs 配置列表
     * @return 是否成功
     */
    boolean saveUserWheelConfigs(Long userId, String wheelType, List<WheelConfig> configs);
    
    /**
     * 初始化用户转盘配置（从默认配置复制）
     * @param userId 用户ID
     * @param wheelType 转盘类型
     * @return 是否成功
     */
    boolean initUserWheelConfigs(Long userId, String wheelType);
    
    /**
     * 转盘旋转
     * @param userId 用户ID
     * @param wheelType 转盘类型
     * @return 转盘结果
     */
    Map<String, Object> spinWheel(Long userId, String wheelType);
    
    /**
     * 获取用户转盘记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 记录列表和总数
     */
    Map<String, Object> getUserWheelRecords(Long userId, int page, int size);
    
    /**
     * 清空用户转盘记录
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean clearUserWheelRecords(Long userId);
    
    /**
     * 获取转盘类型列表
     * @return 转盘类型信息
     */
    List<Map<String, Object>> getWheelTypes();
    
    /**
     * 清理过期记录（定时任务）
     */
    void cleanExpiredRecords();
}
