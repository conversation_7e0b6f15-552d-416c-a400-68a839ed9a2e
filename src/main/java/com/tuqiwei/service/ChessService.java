package com.tuqiwei.service;

import java.util.List;
import java.util.Map;

/**
 * 象棋游戏服务接口
 */
public interface ChessService {
    
    /**
     * 开始新的象棋游戏
     */
    Map<String, Object> startNewChessGame(Long userId);
    
    /**
     * 移动棋子
     */
    Map<String, Object> movePiece(Long userId, String gameId, int fromX, int fromY, int toX, int toY);
    
    /**
     * 悔棋请求
     */
    Map<String, Object> requestUndo(Long userId, String gameId);
    
    /**
     * 响应悔棋请求
     */
    Map<String, Object> respondUndo(Long userId, String gameId, boolean accept);
    
    /**
     * 认输
     */
    Map<String, Object> surrender(Long userId, String gameId);
    
    /**
     * 保存棋局
     */
    Map<String, Object> saveGame(Long userId, String gameId, String gameName);
    
    /**
     * 加载棋局
     */
    Map<String, Object> loadGame(Long userId, Long gameRecordId);
    
    /**
     * 获取用户的象棋游戏记录
     */
    List<Map<String, Object>> getUserChessGames(Long userId, int limit);
    
    /**
     * 获取游戏状态
     */
    Map<String, Object> getGameState(String gameId);
    
    /**
     * 检查是否将军
     */
    boolean isInCheck(String boardState, int playerSide);
    
    /**
     * 检查是否将死
     */
    boolean isCheckmate(String boardState, int playerSide);
    
    /**
     * 验证移动是否合法
     */
    boolean isValidMove(String boardState, int fromX, int fromY, int toX, int toY, int playerSide);
    
    /**
     * 生成棋谱记录
     */
    String generateMoveNotation(String boardState, int fromX, int fromY, int toX, int toY);
}
