package com.tuqiwei.service;

import com.tuqiwei.entity.AdminUser;
import com.tuqiwei.entity.User;

import java.util.List;
import java.util.Map;

/**
 * 管理员服务接口
 */
public interface AdminService {
    
    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    Map<String, Object> login(String username, String password);
    
    /**
     * 获取管理员信息
     * @param adminId 管理员ID
     * @return 管理员信息
     */
    AdminUser getAdminInfo(Long adminId);
    
    /**
     * 分页查询用户列表
     * @param keyword 搜索关键词
     * @param status 用户状态
     * @param page 页码
     * @param size 每页大小
     * @return 用户列表和总数
     */
    Map<String, Object> getUserList(String keyword, Boolean status, int page, int size);
    
    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateUserStatus(Long userId, Boolean status);
    
    /**
     * 获取数据统计概览
     * @return 统计数据
     */
    Map<String, Object> getDashboardStats();
    
    /**
     * 获取用户趋势数据
     * @return 趋势数据
     */
    Map<String, Object> getUserTrendData();
}
