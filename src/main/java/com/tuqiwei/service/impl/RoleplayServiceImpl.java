package com.tuqiwei.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tuqiwei.config.AiConfig;
import com.tuqiwei.entity.RoleplayCharacter;
import com.tuqiwei.entity.RoleplayMessage;
import com.tuqiwei.entity.RoleplaySession;
import com.tuqiwei.mapper.RoleplayCharacterMapper;
import com.tuqiwei.mapper.RoleplayMessageMapper;
import com.tuqiwei.mapper.RoleplaySessionMapper;
import com.tuqiwei.service.RoleplayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.*;

/**
 * 角色扮演服务实现
 */
@Service
public class RoleplayServiceImpl implements RoleplayService {
    
    private static final Logger logger = LoggerFactory.getLogger(RoleplayServiceImpl.class);
    
    @Autowired
    private RoleplayCharacterMapper characterMapper;
    
    @Autowired
    private RoleplaySessionMapper sessionMapper;
    
    @Autowired
    private RoleplayMessageMapper messageMapper;
    
    @Autowired
    private WebClient aiWebClient;
    
    @Autowired
    private AiConfig aiConfig;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public List<RoleplayCharacter> getAllCharacters() {
        try {
            return characterMapper.selectActivePublicCharacters();
        } catch (Exception e) {
            logger.error("Error getting all characters", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<RoleplayCharacter> getCharactersByType(String characterType) {
        try {
            return characterMapper.selectByType(characterType);
        } catch (Exception e) {
            logger.error("Error getting characters by type: {}", characterType, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<RoleplayCharacter> getCharactersByCategory(String category) {
        try {
            return characterMapper.selectByCategory(category);
        } catch (Exception e) {
            logger.error("Error getting characters by category: {}", category, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<RoleplayCharacter> searchCharacters(String keyword) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return getAllCharacters();
            }
            return characterMapper.searchCharacters(keyword.trim());
        } catch (Exception e) {
            logger.error("Error searching characters with keyword: {}", keyword, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public RoleplayCharacter getCharacterById(Long id) {
        try {
            return characterMapper.selectById(id);
        } catch (Exception e) {
            logger.error("Error getting character by id: {}", id, e);
            return null;
        }
    }
    
    @Override
    public Map<String, Object> getCharacterFilters() {
        try {
            Map<String, Object> filters = new HashMap<>();
            filters.put("types", characterMapper.selectCharacterTypes());
            filters.put("categories", characterMapper.selectCategories());
            return filters;
        } catch (Exception e) {
            logger.error("Error getting character filters", e);
            return new HashMap<>();
        }
    }
    
    @Override
    @Transactional
    public RoleplaySession createSession(Long userId, Long characterId, String sessionName, String sessionContext) {
        try {
            RoleplaySession session = new RoleplaySession(userId, characterId, sessionName);
            session.setSessionContext(sessionContext);
            
            sessionMapper.insert(session);
            
            // 增加角色使用次数
            characterMapper.incrementUsageCount(characterId);
            
            return session;
        } catch (Exception e) {
            logger.error("Error creating roleplay session", e);
            return null;
        }
    }
    
    @Override
    public List<RoleplaySession> getUserSessions(Long userId) {
        try {
            return sessionMapper.selectByUserId(userId);
        } catch (Exception e) {
            logger.error("Error getting user sessions for user: {}", userId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public RoleplaySession getSessionById(Long sessionId) {
        try {
            return sessionMapper.selectById(sessionId);
        } catch (Exception e) {
            logger.error("Error getting session by id: {}", sessionId, e);
            return null;
        }
    }
    
    @Override
    public List<RoleplayMessage> getSessionMessages(Long sessionId) {
        try {
            return messageMapper.selectBySessionId(sessionId);
        } catch (Exception e) {
            logger.error("Error getting session messages for session: {}", sessionId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public String sendMessage(Long sessionId, String userMessage, Long userId) {
        try {
            // 保存用户消息
            RoleplayMessage userMsg = new RoleplayMessage(sessionId, userId, "user", userMessage);
            messageMapper.insert(userMsg);

            // 获取会话信息
            RoleplaySession session = sessionMapper.selectById(sessionId);
            if (session == null) {
                return "会话不存在";
            }

            // 获取角色信息
            RoleplayCharacter character = characterMapper.selectById(session.getCharacterId());
            if (character == null) {
                return "角色不存在";
            }

            // 调用DeepSeek API生成角色回复
            String characterReply = callDeepSeekForRoleplay(session, character, userMessage);

            // 如果AI API调用失败，使用默认回复
            if (characterReply == null || characterReply.trim().isEmpty()) {
                characterReply = generateDefaultReply(character, userMessage);
            }

            // 保存角色回复
            RoleplayMessage characterMsg = new RoleplayMessage(sessionId, userId, "character", characterReply);
            messageMapper.insert(characterMsg);

            // 更新会话消息计数
            sessionMapper.incrementMessageCount(sessionId);

            return characterReply;

        } catch (Exception e) {
            logger.error("Error sending roleplay message", e);
            return "抱歉，我现在有点累了，稍后再聊吧～";
        }
    }

    @Override
    @Transactional
    public Map<String, Object> startRoleplaySession(Long characterId, Long userId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取角色信息
            RoleplayCharacter character = characterMapper.selectById(characterId);
            if (character == null) {
                result.put("success", false);
                result.put("message", "角色不存在");
                return result;
            }

            // 检查是否已有活跃会话
            int activeSessionCount = sessionMapper.countActiveSessionsByUserAndCharacter(userId, characterId);
            RoleplaySession session;

            if (activeSessionCount > 0) {
                // 使用现有会话
                List<RoleplaySession> sessions = sessionMapper.selectByUserId(userId);
                session = sessions.stream()
                    .filter(s -> s.getCharacterId().equals(characterId) && s.getIsActive())
                    .findFirst()
                    .orElse(null);
            } else {
                // 创建新会话
                String sessionName = "与" + character.getCharacterName() + "的对话";
                session = createSession(userId, characterId, sessionName, null);
            }

            if (session == null) {
                result.put("success", false);
                result.put("message", "创建会话失败");
                return result;
            }

            result.put("success", true);
            result.put("sessionId", session.getId());
            result.put("character", character);
            result.put("greeting", character.getGreetingMessage());

            return result;

        } catch (Exception e) {
            logger.error("Error starting roleplay session", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "启动会话失败");
            return result;
        }
    }

    @Override
    public boolean toggleMessageFavorite(Long messageId, Long userId) {
        try {
            RoleplayMessage message = messageMapper.selectById(messageId);
            if (message == null || !message.getUserId().equals(userId)) {
                return false;
            }

            boolean newFavoriteStatus = !message.getIsFavorite();
            messageMapper.updateFavoriteStatus(messageId, newFavoriteStatus);
            return true;

        } catch (Exception e) {
            logger.error("Error toggling message favorite", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteSession(Long sessionId, Long userId) {
        try {
            RoleplaySession session = sessionMapper.selectById(sessionId);
            if (session == null || !session.getUserId().equals(userId)) {
                return false;
            }

            // 删除会话的所有消息
            messageMapper.deleteBySessionId(sessionId);

            // 删除会话
            sessionMapper.deleteById(sessionId);

            return true;

        } catch (Exception e) {
            logger.error("Error deleting session", e);
            return false;
        }
    }

    /**
     * 调用DeepSeek API生成角色回复
     */
    private String callDeepSeekForRoleplay(RoleplaySession session, RoleplayCharacter character, String userMessage) {
        try {
            // 构建系统提示词
            String systemPrompt = buildRoleplaySystemPrompt(character, session);

            // 获取最近的对话历史
            List<RoleplayMessage> recentMessages = messageMapper.selectRecentMessages(session.getId(), 10);
            Collections.reverse(recentMessages); // 按时间正序排列

            // 构建请求体
            Map<String, Object> requestBody = buildRoleplayRequest(systemPrompt, recentMessages, userMessage);

            // 发送请求到DeepSeek API
            String response = aiWebClient.post()
                    .uri("/v1/chat/completions")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            // 解析响应
            return parseAIResponse(response);

        } catch (Exception e) {
            logger.error("Error calling DeepSeek API for roleplay", e);
            return null;
        }
    }

    /**
     * 构建角色扮演系统提示词
     */
    private String buildRoleplaySystemPrompt(RoleplayCharacter character, RoleplaySession session) {
        StringBuilder prompt = new StringBuilder();

        // 基础角色设定
        prompt.append("你现在要扮演：").append(character.getCharacterName()).append("\n\n");

        // 角色描述
        if (character.getDescription() != null && !character.getDescription().isEmpty()) {
            prompt.append("角色描述：").append(character.getDescription()).append("\n\n");
        }

        // 性格特点
        if (character.getPersonality() != null && !character.getPersonality().isEmpty()) {
            prompt.append("性格特点：").append(character.getPersonality()).append("\n\n");
        }

        // 说话风格
        if (character.getSpeakingStyle() != null && !character.getSpeakingStyle().isEmpty()) {
            prompt.append("说话风格：").append(character.getSpeakingStyle()).append("\n\n");
        }

        // 背景故事
        if (character.getBackgroundStory() != null && !character.getBackgroundStory().isEmpty()) {
            prompt.append("背景故事：").append(character.getBackgroundStory()).append("\n\n");
        }

        // 会话背景
        if (session.getSessionContext() != null && !session.getSessionContext().isEmpty()) {
            prompt.append("当前情境：").append(session.getSessionContext()).append("\n\n");
        }

        // 系统提示词
        if (character.getSystemPrompt() != null && !character.getSystemPrompt().isEmpty()) {
            prompt.append(character.getSystemPrompt()).append("\n\n");
        }

        // 扮演要求
        prompt.append("请严格按照以上设定进行角色扮演，保持角色的一致性。");
        prompt.append("回复要自然流畅，符合角色的性格和说话风格。");
        prompt.append("回复长度控制在50-150字之间，不要过于冗长。");

        return prompt.toString();
    }

    /**
     * 构建角色扮演请求体
     */
    private Map<String, Object> buildRoleplayRequest(String systemPrompt, List<RoleplayMessage> recentMessages, String userMessage) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", aiConfig.getChat().getOptions().getModel());
        requestBody.put("temperature", 0.9); // 提高创造性，让角色更生动
        requestBody.put("max_tokens", 300); // 适当的回复长度
        requestBody.put("stream", false); // 非流式响应

        // 构建消息列表
        List<Map<String, String>> messages = new ArrayList<>();

        // 系统消息
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", systemPrompt);
        messages.add(systemMessage);

        // 添加历史对话（保持上下文）
        for (RoleplayMessage msg : recentMessages) {
            Map<String, String> message = new HashMap<>();
            if ("user".equals(msg.getRole())) {
                message.put("role", "user");
            } else {
                message.put("role", "assistant");
            }
            message.put("content", msg.getContent());
            messages.add(message);
        }

        // 当前用户消息
        Map<String, String> userMsg = new HashMap<>();
        userMsg.put("role", "user");
        userMsg.put("content", userMessage);
        messages.add(userMsg);

        requestBody.put("messages", messages);

        return requestBody;
    }

    /**
     * 解析AI响应
     */
    private String parseAIResponse(String response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode choices = jsonNode.get("choices");
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode message = choices.get(0).get("message");
                if (message != null && message.has("content")) {
                    return message.get("content").asText().trim();
                }
            }
        } catch (Exception e) {
            logger.error("Error parsing AI response: {}", response, e);
        }
        return null;
    }

    /**
     * 生成默认回复（当AI API失败时使用）
     */
    private String generateDefaultReply(RoleplayCharacter character, String userMessage) {
        // 根据角色类型和分类生成不同的默认回复
        String characterName = character.getCharacterName();
        String category = character.getCategory();

        if ("恋人".equals(category)) {
            return "我现在有点累了，但还是想陪着你～你刚才说什么？";
        } else if ("朋友".equals(category)) {
            return "哈哈，我刚才走神了，你能再说一遍吗？";
        } else if ("老师".equals(category)) {
            return "嗯，这个问题很有意思，让我想想怎么回答你。";
        } else {
            return "抱歉，我刚才在想别的事情，你能重复一下吗？";
        }
    }
}
