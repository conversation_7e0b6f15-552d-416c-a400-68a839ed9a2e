package com.tuqiwei.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tuqiwei.entity.ChessGame;
import com.tuqiwei.entity.UserGameRecord;
import com.tuqiwei.service.ChessService;
import com.tuqiwei.service.GameService;
import com.tuqiwei.util.ChessUtil;
import com.tuqiwei.util.ChessMoveValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 象棋游戏服务实现
 */
@Service
public class ChessServiceImpl implements ChessService {
    
    @Autowired
    private GameService gameService;
    
    // 内存中存储当前进行的游戏
    private final Map<String, ChessGame> activeGames = new ConcurrentHashMap<>();
    
    @Override
    public Map<String, Object> startNewChessGame(Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 生成游戏ID
            String gameId = "chess_" + userId + "_" + System.currentTimeMillis();
            
            // 创建新游戏
            ChessGame game = new ChessGame(gameId, userId);
            game.setMoveHistory(new ArrayList<>());
            game.setNotationHistory(new ArrayList<>());
            
            // 存储到内存
            activeGames.put(gameId, game);
            
            result.put("success", true);
            result.put("gameId", gameId);
            result.put("boardState", game.getBoardState());
            result.put("currentPlayer", game.getCurrentPlayer());
            result.put("gameStatus", game.getGameStatus());
            result.put("message", "象棋游戏开始！红方先行");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建游戏失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> movePiece(Long userId, String gameId, int fromX, int fromY, int toX, int toY) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ChessGame game = activeGames.get(gameId);
            if (game == null) {
                result.put("success", false);
                result.put("message", "游戏不存在");
                return result;
            }
            
            if (!game.getGameStatus().equals("PLAYING")) {
                result.put("success", false);
                result.put("message", "游戏已结束");
                return result;
            }
            
            // 解析棋盘状态
            int[][] board = ChessUtil.parseBoardState(game.getBoardState());
            
            // 验证移动是否合法
            if (!ChessMoveValidator.isValidMove(board, fromX, fromY, toX, toY, game.getCurrentPlayer())) {
                result.put("success", false);
                result.put("message", "非法移动");
                return result;
            }
            
            // 执行移动
            int piece = board[fromX][fromY];
            int capturedPiece = board[toX][toY];
            board[fromX][fromY] = ChessUtil.EMPTY;
            board[toX][toY] = piece;
            
            // 检查移动后是否将军自己
            if (isInCheck(ChessUtil.boardStateToJson(board), game.getCurrentPlayer())) {
                result.put("success", false);
                result.put("message", "此移动会导致己方被将军");
                return result;
            }
            
            // 更新游戏状态
            game.setBoardState(ChessUtil.boardStateToJson(board));
            
            // 记录移动
            String moveString = ChessUtil.generateMoveString(fromX, fromY, toX, toY);
            game.getMoveHistory().add(moveString);
            
            // 生成棋谱记录
            String notation = generateMoveNotation(game.getBoardState(), fromX, fromY, toX, toY);
            game.getNotationHistory().add(notation);
            
            // 切换玩家
            game.setCurrentPlayer(game.getCurrentPlayer() == 1 ? 2 : 1);
            
            // 检查对方是否被将军或将死
            boolean inCheck = isInCheck(game.getBoardState(), game.getCurrentPlayer());
            boolean checkmate = false;
            
            if (inCheck) {
                checkmate = isCheckmate(game.getBoardState(), game.getCurrentPlayer());
                if (checkmate) {
                    game.setGameStatus("CHECKMATE");
                    game.setWinner(game.getCurrentPlayer() == 1 ? "BLACK" : "RED");
                }
            }
            
            result.put("success", true);
            result.put("boardState", game.getBoardState());
            result.put("currentPlayer", game.getCurrentPlayer());
            result.put("gameStatus", game.getGameStatus());
            result.put("inCheck", inCheck);
            result.put("checkmate", checkmate);
            result.put("capturedPiece", capturedPiece);
            result.put("moveNotation", notation);
            result.put("moveHistory", game.getNotationHistory());
            
            if (checkmate) {
                result.put("message", "将死！" + (game.getWinner().equals("RED") ? "红方" : "黑方") + "获胜！");
                // 保存游戏结果
                saveGameResult(game);
            } else if (inCheck) {
                result.put("message", "将军！");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "移动失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> requestUndo(Long userId, String gameId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ChessGame game = activeGames.get(gameId);
            if (game == null) {
                result.put("success", false);
                result.put("message", "游戏不存在");
                return result;
            }
            
            if (game.getMoveHistory().isEmpty()) {
                result.put("success", false);
                result.put("message", "没有可悔棋的步数");
                return result;
            }
            
            if (game.isUndoRequested()) {
                result.put("success", false);
                result.put("message", "已有悔棋请求等待处理");
                return result;
            }
            
            // 设置悔棋请求
            game.setUndoRequested(true);
            game.setUndoRequestPlayer(game.getCurrentPlayer() == 1 ? 2 : 1); // 请求方是上一步的玩家
            
            result.put("success", true);
            result.put("message", "悔棋请求已发送，等待对方同意");
            result.put("undoRequested", true);
            result.put("undoRequestPlayer", game.getUndoRequestPlayer());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "悔棋请求失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> respondUndo(Long userId, String gameId, boolean accept) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ChessGame game = activeGames.get(gameId);
            if (game == null) {
                result.put("success", false);
                result.put("message", "游戏不存在");
                return result;
            }
            
            if (!game.isUndoRequested()) {
                result.put("success", false);
                result.put("message", "没有悔棋请求");
                return result;
            }
            
            if (accept) {
                // 同意悔棋，回退一步
                if (!game.getMoveHistory().isEmpty()) {
                    // 移除最后一步
                    game.getMoveHistory().remove(game.getMoveHistory().size() - 1);
                    game.getNotationHistory().remove(game.getNotationHistory().size() - 1);
                    
                    // 重新构建棋盘状态
                    game.setBoardState(rebuildBoardState(game.getMoveHistory()));
                    
                    // 切换回上一个玩家
                    game.setCurrentPlayer(game.getCurrentPlayer() == 1 ? 2 : 1);
                }
                
                result.put("message", "悔棋成功");
                result.put("boardState", game.getBoardState());
                result.put("currentPlayer", game.getCurrentPlayer());
                result.put("moveHistory", game.getNotationHistory());
            } else {
                result.put("message", "悔棋被拒绝");
            }
            
            // 清除悔棋请求
            game.setUndoRequested(false);
            game.setUndoRequestPlayer(0);
            
            result.put("success", true);
            result.put("undoRequested", false);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "处理悔棋响应失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> surrender(Long userId, String gameId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ChessGame game = activeGames.get(gameId);
            if (game == null) {
                result.put("success", false);
                result.put("message", "游戏不存在");
                return result;
            }
            
            // 设置游戏结束
            game.setGameStatus("SURRENDER");
            game.setWinner(game.getCurrentPlayer() == 1 ? "BLACK" : "RED");
            
            // 保存游戏结果
            saveGameResult(game);
            
            result.put("success", true);
            result.put("message", (game.getCurrentPlayer() == 1 ? "红方" : "黑方") + "认输，" + 
                                 (game.getWinner().equals("RED") ? "红方" : "黑方") + "获胜！");
            result.put("gameStatus", game.getGameStatus());
            result.put("winner", game.getWinner());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "认输失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> saveGame(Long userId, String gameId, String gameName) {
        Map<String, Object> result = new HashMap<>();

        try {
            ChessGame game = activeGames.get(gameId);
            if (game == null) {
                result.put("success", false);
                result.put("message", "游戏不存在");
                return result;
            }

            // 计算游戏时长
            long currentTime = System.currentTimeMillis();
            int playTime = (int) ((currentTime - game.getStartTime()) / 1000);

            // 创建游戏记录
            UserGameRecord record = new UserGameRecord(userId, "chess", 0L, 1);
            record.setGameData(createGameData(game, gameName));
            record.setPlayTime(playTime);
            record.setIsCompleted(false);

            // 保存到数据库
            boolean saved = gameService.saveGameRecord(record);

            if (saved) {
                result.put("success", true);
                result.put("message", "棋局保存成功");
            } else {
                result.put("success", false);
                result.put("message", "棋局保存失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "保存棋局失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> loadGame(Long userId, Long gameRecordId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从数据库加载游戏记录
            List<UserGameRecord> records = gameService.getUserGameRecords(userId, "chess", 100);
            UserGameRecord targetRecord = null;

            for (UserGameRecord record : records) {
                if (record.getId().equals(gameRecordId)) {
                    targetRecord = record;
                    break;
                }
            }

            if (targetRecord == null) {
                result.put("success", false);
                result.put("message", "棋局记录不存在");
                return result;
            }

            // 解析游戏数据并重建游戏
            ChessGame game = parseGameData(targetRecord.getGameData(), userId);

            // 生成新的游戏ID
            String gameId = "chess_" + userId + "_" + System.currentTimeMillis();
            game.setGameId(gameId);

            // 存储到内存
            activeGames.put(gameId, game);

            result.put("success", true);
            result.put("gameId", gameId);
            result.put("boardState", game.getBoardState());
            result.put("currentPlayer", game.getCurrentPlayer());
            result.put("gameStatus", game.getGameStatus());
            result.put("moveHistory", game.getNotationHistory());
            result.put("message", "棋局加载成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "加载棋局失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getUserChessGames(Long userId, int limit) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            List<UserGameRecord> records = gameService.getUserGameRecords(userId, "chess", limit);

            for (UserGameRecord record : records) {
                Map<String, Object> gameInfo = new HashMap<>();
                gameInfo.put("id", record.getId());
                gameInfo.put("playTime", record.getPlayTime());
                gameInfo.put("createdTime", record.getCreatedTime());
                gameInfo.put("isCompleted", record.getIsCompleted());

                // 解析游戏数据获取更多信息
                try {
                    Map<String, Object> gameData = parseGameDataToMap(record.getGameData());
                    gameInfo.put("gameName", gameData.get("gameName"));
                    gameInfo.put("winner", gameData.get("winner"));
                    gameInfo.put("moveCount", gameData.get("moveCount"));
                } catch (Exception e) {
                    gameInfo.put("gameName", "未命名棋局");
                }

                result.add(gameInfo);
            }

        } catch (Exception e) {
            // 返回空列表
        }

        return result;
    }

    @Override
    public Map<String, Object> getGameState(String gameId) {
        Map<String, Object> result = new HashMap<>();

        ChessGame game = activeGames.get(gameId);
        if (game == null) {
            result.put("success", false);
            result.put("message", "游戏不存在");
            return result;
        }

        result.put("success", true);
        result.put("boardState", game.getBoardState());
        result.put("currentPlayer", game.getCurrentPlayer());
        result.put("gameStatus", game.getGameStatus());
        result.put("moveHistory", game.getNotationHistory());
        result.put("undoRequested", game.isUndoRequested());
        result.put("undoRequestPlayer", game.getUndoRequestPlayer());
        result.put("winner", game.getWinner());

        return result;
    }

    @Override
    public boolean isInCheck(String boardState, int playerSide) {
        try {
            int[][] board = ChessUtil.parseBoardState(boardState);

            // 找到己方帅/将的位置
            int kingX = -1, kingY = -1;
            int kingPiece = (playerSide == 1) ? ChessUtil.RED_KING : ChessUtil.BLACK_KING;

            for (int x = 0; x < 10; x++) {
                for (int y = 0; y < 9; y++) {
                    if (board[x][y] == kingPiece) {
                        kingX = x;
                        kingY = y;
                        break;
                    }
                }
                if (kingX != -1) break;
            }

            if (kingX == -1) return false; // 找不到帅/将

            // 检查是否被对方棋子攻击
            int opponent = (playerSide == 1) ? 2 : 1;
            for (int x = 0; x < 10; x++) {
                for (int y = 0; y < 9; y++) {
                    int piece = board[x][y];
                    if (piece != ChessUtil.EMPTY && ChessUtil.isPieceOfPlayer(piece, opponent)) {
                        if (ChessMoveValidator.isValidMove(board, x, y, kingX, kingY, opponent)) {
                            return true;
                        }
                    }
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean isCheckmate(String boardState, int playerSide) {
        try {
            int[][] board = ChessUtil.parseBoardState(boardState);

            // 尝试所有可能的移动，看是否能解除将军
            for (int fromX = 0; fromX < 10; fromX++) {
                for (int fromY = 0; fromY < 9; fromY++) {
                    int piece = board[fromX][fromY];
                    if (piece != ChessUtil.EMPTY && ChessUtil.isPieceOfPlayer(piece, playerSide)) {

                        for (int toX = 0; toX < 10; toX++) {
                            for (int toY = 0; toY < 9; toY++) {
                                if (ChessMoveValidator.isValidMove(board, fromX, fromY, toX, toY, playerSide)) {
                                    // 模拟移动
                                    int originalPiece = board[toX][toY];
                                    board[toX][toY] = board[fromX][fromY];
                                    board[fromX][fromY] = ChessUtil.EMPTY;

                                    // 检查移动后是否还在被将军
                                    boolean stillInCheck = isInCheck(ChessUtil.boardStateToJson(board), playerSide);

                                    // 恢复棋盘
                                    board[fromX][fromY] = board[toX][toY];
                                    board[toX][toY] = originalPiece;

                                    if (!stillInCheck) {
                                        return false; // 找到了解除将军的移动
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return true; // 没有找到解除将军的移动
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean isValidMove(String boardState, int fromX, int fromY, int toX, int toY, int playerSide) {
        try {
            int[][] board = ChessUtil.parseBoardState(boardState);
            return ChessMoveValidator.isValidMove(board, fromX, fromY, toX, toY, playerSide);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String generateMoveNotation(String boardState, int fromX, int fromY, int toX, int toY) {
        try {
            int[][] board = ChessUtil.parseBoardState(boardState);
            int piece = board[fromX][fromY];

            // 简化的记谱法实现
            String pieceName = ChessUtil.getPieceName(piece);
            String from = convertPositionToNotation(fromX, fromY);
            String to = convertPositionToNotation(toX, toY);

            return pieceName + from + "-" + to;
        } catch (Exception e) {
            return "未知移动";
        }
    }

    // 私有辅助方法

    /**
     * 保存游戏结果
     */
    private void saveGameResult(ChessGame game) {
        try {
            long currentTime = System.currentTimeMillis();
            int playTime = (int) ((currentTime - game.getStartTime()) / 1000);

            UserGameRecord record = new UserGameRecord(game.getUserId(), "chess", 0L, 1);
            record.setGameData(createGameData(game, "对局记录"));
            record.setPlayTime(playTime);
            record.setIsCompleted(true);

            gameService.saveGameRecord(record);

            // 从内存中移除游戏
            activeGames.remove(game.getGameId());
        } catch (Exception e) {
            // 记录日志但不抛出异常
        }
    }

    /**
     * 创建游戏数据JSON
     */
    private String createGameData(ChessGame game, String gameName) {
        try {
            Map<String, Object> gameData = new HashMap<>();
            gameData.put("gameName", gameName);
            gameData.put("boardState", game.getBoardState());
            gameData.put("currentPlayer", game.getCurrentPlayer());
            gameData.put("gameStatus", game.getGameStatus());
            gameData.put("moveHistory", game.getMoveHistory());
            gameData.put("notationHistory", game.getNotationHistory());
            gameData.put("winner", game.getWinner());
            gameData.put("moveCount", game.getMoveHistory().size());

            return new ObjectMapper().writeValueAsString(gameData);
        } catch (Exception e) {
            return "{}";
        }
    }

    /**
     * 解析游戏数据
     */
    private ChessGame parseGameData(String gameDataJson, Long userId) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> gameData = mapper.readValue(gameDataJson, Map.class);

            ChessGame game = new ChessGame();
            game.setUserId(userId);
            game.setBoardState((String) gameData.get("boardState"));
            game.setCurrentPlayer((Integer) gameData.get("currentPlayer"));
            game.setGameStatus((String) gameData.get("gameStatus"));
            game.setMoveHistory((List<String>) gameData.get("moveHistory"));
            game.setNotationHistory((List<String>) gameData.get("notationHistory"));
            game.setWinner((String) gameData.get("winner"));

            return game;
        } catch (Exception e) {
            throw new RuntimeException("解析游戏数据失败", e);
        }
    }

    /**
     * 解析游戏数据为Map
     */
    private Map<String, Object> parseGameDataToMap(String gameDataJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(gameDataJson, Map.class);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    /**
     * 重建棋盘状态
     */
    private String rebuildBoardState(List<String> moveHistory) {
        // 从初始状态开始重新执行所有移动
        ChessGame tempGame = new ChessGame("temp", 0L);
        int[][] board = ChessUtil.parseBoardState(tempGame.getBoardState());

        for (String moveString : moveHistory) {
            int[] move = ChessUtil.parseMove(moveString);
            int piece = board[move[0]][move[1]];
            board[move[0]][move[1]] = ChessUtil.EMPTY;
            board[move[2]][move[3]] = piece;
        }

        return ChessUtil.boardStateToJson(board);
    }

    /**
     * 将位置转换为记谱法表示
     */
    private String convertPositionToNotation(int x, int y) {
        // 简化实现，返回数字坐标
        return (x + 1) + "" + (y + 1);
    }
}
