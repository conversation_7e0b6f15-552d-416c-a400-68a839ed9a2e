package com.tuqiwei.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tuqiwei.config.AiConfig;
import com.tuqiwei.entity.ComfortScenario;
import com.tuqiwei.entity.MoodRecord;
import com.tuqiwei.mapper.ComfortScenarioMapper;
import com.tuqiwei.mapper.MoodRecordMapper;
import com.tuqiwei.service.ComfortService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 哄哄模拟器服务实现
 */
@Service
public class ComfortServiceImpl implements ComfortService {
    
    private static final Logger logger = LoggerFactory.getLogger(ComfortServiceImpl.class);
    
    @Autowired
    private ComfortScenarioMapper comfortScenarioMapper;

    @Autowired
    private MoodRecordMapper moodRecordMapper;

    @Autowired
    private WebClient aiWebClient;

    @Autowired
    private AiConfig aiConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 情绪关键词映射
    private static final Map<String, List<String>> MOOD_KEYWORDS = new HashMap<>();
    
    static {
        MOOD_KEYWORDS.put("sad", Arrays.asList("难过", "伤心", "痛苦", "失落", "沮丧", "郁闷", "心情不好", "不开心", "哭", "眼泪"));
        MOOD_KEYWORDS.put("angry", Arrays.asList("生气", "愤怒", "火大", "气死了", "烦躁", "恼火", "讨厌", "受不了", "气愤", "暴躁"));
        MOOD_KEYWORDS.put("stressed", Arrays.asList("压力", "焦虑", "紧张", "担心", "忙", "累", "疲惫", "烦", "头疼", "崩溃"));
        MOOD_KEYWORDS.put("lonely", Arrays.asList("孤独", "寂寞", "一个人", "没人", "孤单", "空虚", "无聊", "想念", "思念", "陪伴"));
        MOOD_KEYWORDS.put("tired", Arrays.asList("累", "疲惫", "困", "没精神", "乏力", "疲劳", "想睡", "没力气", "筋疲力尽", "身心俱疲"));
    }
    
    @Override
    public List<ComfortScenario> getAvailableScenarios() {
        try {
            return comfortScenarioMapper.selectActiveScenarios();
        } catch (Exception e) {
            logger.error("Error getting available scenarios", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ComfortScenario> recommendScenarios(String moodType) {
        try {
            return comfortScenarioMapper.selectByMoodType(moodType);
        } catch (Exception e) {
            logger.error("Error recommending scenarios for mood: " + moodType, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public String analyzeMood(String userMessage) {
        if (userMessage == null || userMessage.trim().isEmpty()) {
            return "neutral";
        }
        
        String message = userMessage.toLowerCase();
        Map<String, Integer> moodScores = new HashMap<>();
        
        // 计算每种情绪的匹配分数
        for (Map.Entry<String, List<String>> entry : MOOD_KEYWORDS.entrySet()) {
            String mood = entry.getKey();
            List<String> keywords = entry.getValue();
            int score = 0;
            
            for (String keyword : keywords) {
                if (message.contains(keyword)) {
                    score += keyword.length(); // 关键词越长，权重越高
                }
            }
            
            if (score > 0) {
                moodScores.put(mood, score);
            }
        }
        
        // 返回得分最高的情绪类型
        return moodScores.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("neutral");
    }
    
    @Override
    public void recordMood(Long userId, Long sessionId, String moodType, Integer moodLevel, 
                          String description, String triggerEvent) {
        try {
            MoodRecord record = new MoodRecord(userId, sessionId, moodType, moodLevel, description, triggerEvent);
            moodRecordMapper.insert(record);
        } catch (Exception e) {
            logger.error("Error recording mood", e);
        }
    }
    
    @Override
    public List<MoodRecord> getUserMoodHistory(Long userId) {
        try {
            return moodRecordMapper.selectByUserId(userId);
        } catch (Exception e) {
            logger.error("Error getting user mood history for user: " + userId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public Map<String, Object> getUserMoodStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            List<MoodRecord> records = moodRecordMapper.selectByUserId(userId);
            
            // 统计各种心情的次数
            Map<String, Integer> moodCounts = new HashMap<>();
            int totalRecords = records.size();
            double averageMoodLevel = 0.0;
            
            for (MoodRecord record : records) {
                String moodType = record.getMoodType();
                moodCounts.put(moodType, moodCounts.getOrDefault(moodType, 0) + 1);
                averageMoodLevel += record.getMoodLevel();
            }
            
            if (totalRecords > 0) {
                averageMoodLevel /= totalRecords;
            }
            
            stats.put("totalRecords", totalRecords);
            stats.put("moodCounts", moodCounts);
            stats.put("averageMoodLevel", Math.round(averageMoodLevel * 100.0) / 100.0);
            
            // 最近的心情记录
            MoodRecord latestMood = moodRecordMapper.selectLatestByUserId(userId);
            stats.put("latestMood", latestMood);
            
        } catch (Exception e) {
            logger.error("Error getting user mood stats for user: " + userId, e);
        }
        
        return stats;
    }
    
    @Override
    public String generateComfortReply(Long scenarioId, String userMessage, String moodType) {
        try {
            ComfortScenario scenario = comfortScenarioMapper.selectById(scenarioId);
            if (scenario == null) {
                return "我理解你现在的感受，我会一直陪着你的。";
            }

            // 调用DeepSeek API生成个性化安慰回复
            String aiReply = callDeepSeekAPI(scenario, userMessage, moodType);

            // 如果AI API调用失败，使用模板回复作为备用
            if (aiReply == null || aiReply.trim().isEmpty()) {
                aiReply = generateTemplateReply(scenario, userMessage, moodType);
            }

            return aiReply;

        } catch (Exception e) {
            logger.error("Error generating comfort reply", e);
            // 出错时返回模板回复
            return generateTemplateReply(null, userMessage, moodType);
        }
    }
    
    @Override
    public String getScenarioGreeting(Long scenarioId) {
        try {
            ComfortScenario scenario = comfortScenarioMapper.selectById(scenarioId);
            return scenario != null ? scenario.getGreetingMessage() : "你好，我来陪陪你吧～";
        } catch (Exception e) {
            logger.error("Error getting scenario greeting", e);
            return "你好，我来陪陪你吧～";
        }
    }
    
    @Override
    public void updateScenarioUsage(Long scenarioId) {
        try {
            comfortScenarioMapper.incrementUsageCount(scenarioId);
        } catch (Exception e) {
            logger.error("Error updating scenario usage", e);
        }
    }
    
    /**
     * 调用DeepSeek API生成安慰回复
     */
    private String callDeepSeekAPI(ComfortScenario scenario, String userMessage, String moodType) {
        try {
            // 构建系统提示词
            String systemPrompt = buildSystemPrompt(scenario, moodType);

            // 构建请求体
            Map<String, Object> requestBody = buildComfortRequest(systemPrompt, userMessage);

            // 发送请求到DeepSeek API
            String response = aiWebClient.post()
                    .uri("/v1/chat/completions")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            // 解析响应
            return parseAIResponse(response);

        } catch (Exception e) {
            logger.error("Error calling DeepSeek API for comfort reply", e);
            return null;
        }
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(ComfortScenario scenario, String moodType) {
        StringBuilder prompt = new StringBuilder();

        // 基础角色设定
        prompt.append("你是一个温暖、体贴、善解人意的心理安慰师。");

        // 场景特定的角色设定
        if (scenario != null) {
            prompt.append(scenario.getSystemPrompt());
        }

        // 心情特定的指导
        prompt.append("\n\n当前用户的心情状态是：").append(getMoodDescription(moodType));

        // 安慰原则
        prompt.append("\n\n请遵循以下原则：");
        prompt.append("\n1. 用温暖、理解的语气回复");
        prompt.append("\n2. 先共情用户的感受，不要急于给建议");
        prompt.append("\n3. 回复要简洁温暖，不要过于冗长");
        prompt.append("\n4. 避免说教和空洞的安慰");
        prompt.append("\n5. 可以适当使用温暖的表情符号");
        prompt.append("\n6. 回复长度控制在50-100字之间");

        return prompt.toString();
    }

    /**
     * 获取心情描述
     */
    private String getMoodDescription(String moodType) {
        switch (moodType) {
            case "sad": return "难过、伤心";
            case "angry": return "生气、愤怒";
            case "stressed": return "压力大、焦虑";
            case "lonely": return "孤独、寂寞";
            case "tired": return "疲惫、累";
            default: return "情绪低落";
        }
    }

    /**
     * 构建安慰请求体
     */
    private Map<String, Object> buildComfortRequest(String systemPrompt, String userMessage) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", aiConfig.getChat().getOptions().getModel());
        requestBody.put("temperature", 0.8); // 稍微提高创造性
        requestBody.put("max_tokens", 200); // 限制回复长度
        requestBody.put("stream", false); // 非流式响应

        // 构建消息列表
        List<Map<String, String>> messages = new ArrayList<>();

        // 系统消息
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", systemPrompt);
        messages.add(systemMessage);

        // 用户消息
        Map<String, String> userMsg = new HashMap<>();
        userMsg.put("role", "user");
        userMsg.put("content", userMessage);
        messages.add(userMsg);

        requestBody.put("messages", messages);

        return requestBody;
    }

    /**
     * 解析AI响应
     */
    private String parseAIResponse(String response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode choices = jsonNode.get("choices");
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode message = choices.get(0).get("message");
                if (message != null && message.has("content")) {
                    return message.get("content").asText().trim();
                }
            }
        } catch (Exception e) {
            logger.error("Error parsing AI response: {}", response, e);
        }
        return null;
    }

    /**
     * 生成基于模板的回复
     */
    private String generateTemplateReply(ComfortScenario scenario, String userMessage, String moodType) {
        // 根据不同的心情类型和场景生成不同的回复
        List<String> replies = new ArrayList<>();
        
        switch (moodType) {
            case "sad":
                replies.add("我能感受到你现在很难过，哭出来也没关系，我会一直陪着你的。");
                replies.add("虽然现在很痛苦，但请相信这只是暂时的，我们一起度过这个难关好吗？");
                replies.add("你的感受我都理解，不要一个人承受，我在这里陪你。");
                break;
            case "angry":
                replies.add("我理解你现在很生气，深呼吸一下，我们慢慢聊聊发生了什么。");
                replies.add("生气是很正常的情绪，让我们一起想想怎么处理这件事好吗？");
                replies.add("我知道你很愤怒，但请不要伤害自己，我们一起面对。");
                break;
            case "stressed":
                replies.add("我看得出来你压力很大，先放松一下，我们一步一步来解决。");
                replies.add("压力大的时候记得要好好休息，你已经做得很好了。");
                replies.add("不要给自己太大压力，我相信你能处理好的，我支持你。");
                break;
            case "lonely":
                replies.add("你不是一个人，我在这里陪着你，随时都可以找我聊天。");
                replies.add("孤独的时候想想那些关心你的人，包括我，我们都在乎你。");
                replies.add("虽然现在感觉孤单，但请记住你是被爱着的。");
                break;
            case "tired":
                replies.add("你辛苦了，累的时候就好好休息，身体健康最重要。");
                replies.add("感觉疲惫是身体在提醒你需要休息了，听听身体的声音吧。");
                replies.add("你已经很努力了，现在该好好照顾自己了。");
                break;
            default:
                replies.add("我理解你现在的感受，无论发生什么，我都会陪着你的。");
                replies.add("有什么想说的都可以告诉我，我会认真听的。");
        }
        
        // 随机选择一个回复
        Random random = new Random();
        return replies.get(random.nextInt(replies.size()));
    }
}
