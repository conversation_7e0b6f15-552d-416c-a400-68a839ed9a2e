package com.tuqiwei.service.impl;

import com.tuqiwei.entity.GameProgress;
import com.tuqiwei.entity.GameType;
import com.tuqiwei.entity.UserGameRecord;
import com.tuqiwei.entity.UserGameStats;
import com.tuqiwei.mapper.GameMapper;
import com.tuqiwei.service.GameService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 游戏服务实现
 */
@Service
public class GameServiceImpl implements GameService {
    
    private static final Logger logger = LoggerFactory.getLogger(GameServiceImpl.class);
    
    @Autowired
    private GameMapper gameMapper;
    
    @Override
    public List<GameType> getAllActiveGames() {
        try {
            return gameMapper.selectActiveGameTypes();
        } catch (Exception e) {
            logger.error("Error getting active games", e);
            return List.of();
        }
    }
    
    @Override
    public GameType getGameByCode(String gameCode) {
        try {
            return gameMapper.selectGameTypeByCode(gameCode);
        } catch (Exception e) {
            logger.error("Error getting game by code: {}", gameCode, e);
            return null;
        }
    }
    
    @Override
    public UserGameStats getUserGameStats(Long userId, String gameCode) {
        try {
            UserGameStats stats = gameMapper.selectUserGameStats(userId, gameCode);
            if (stats == null) {
                // 如果没有统计记录，创建一个新的
                stats = new UserGameStats(userId, gameCode);
                gameMapper.insertUserGameStats(stats);
            }
            return stats;
        } catch (Exception e) {
            logger.error("Error getting user game stats", e);
            return null;
        }
    }
    
    @Override
    @Transactional
    public boolean saveGameRecord(UserGameRecord record) {
        try {
            return gameMapper.insertGameRecord(record) > 0;
        } catch (Exception e) {
            logger.error("Error saving game record", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean updateUserGameStats(Long userId, String gameCode, Long score, Integer playTime) {
        try {
            UserGameStats stats = getUserGameStats(userId, gameCode);
            if (stats == null) {
                return false;
            }
            
            // 更新统计数据
            stats.setTotalGames(stats.getTotalGames() + 1);
            stats.updateTopScores(score);
            stats.setTotalPlayTime(stats.getTotalPlayTime() + playTime);
            stats.setLastPlayTime(LocalDateTime.now());
            
            return gameMapper.updateUserGameStats(stats) > 0;
        } catch (Exception e) {
            logger.error("Error updating user game stats", e);
            return false;
        }
    }
    
    @Override
    public List<UserGameRecord> getUserGameRecords(Long userId, String gameCode, int limit) {
        try {
            return gameMapper.selectUserGameRecords(userId, gameCode, limit);
        } catch (Exception e) {
            logger.error("Error getting user game records", e);
            return List.of();
        }
    }
    
    @Override
    @Transactional
    public boolean saveGameProgress(GameProgress progress) {
        try {
            GameProgress existing = gameMapper.selectGameProgress(progress.getUserId(), progress.getGameCode());
            if (existing != null) {
                return gameMapper.updateGameProgress(progress) > 0;
            } else {
                return gameMapper.insertGameProgress(progress) > 0;
            }
        } catch (Exception e) {
            logger.error("Error saving game progress", e);
            return false;
        }
    }
    
    @Override
    public GameProgress getGameProgress(Long userId, String gameCode) {
        try {
            return gameMapper.selectGameProgress(userId, gameCode);
        } catch (Exception e) {
            logger.error("Error getting game progress", e);
            return null;
        }
    }
    
    @Override
    @Transactional
    public boolean deleteGameProgress(Long userId, String gameCode) {
        try {
            return gameMapper.deleteGameProgress(userId, gameCode) > 0;
        } catch (Exception e) {
            logger.error("Error deleting game progress", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> startNewGame(Long userId, String gameCode, Integer level) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 验证游戏是否存在
            GameType gameType = getGameByCode(gameCode);
            if (gameType == null) {
                result.put("success", false);
                result.put("message", "游戏不存在");
                return result;
            }

            // 删除之前的游戏进度
            deleteGameProgress(userId, gameCode);

            // 获取用户游戏统计
            UserGameStats stats = getUserGameStats(userId, gameCode);

            result.put("success", true);
            result.put("gameType", gameType);
            result.put("userStats", stats);
            result.put("level", level);
            result.put("message", "新游戏开始");

            return result;
        } catch (Exception e) {
            logger.error("Error starting new game", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "开始游戏失败");
            return result;
        }
    }

    @Override
    public Map<String, Object> continueGame(Long userId, String gameCode) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取游戏进度
            GameProgress progress = getGameProgress(userId, gameCode);
            if (progress == null) {
                result.put("success", false);
                result.put("message", "没有找到游戏进度");
                return result;
            }

            // 获取游戏类型
            GameType gameType = getGameByCode(gameCode);

            result.put("success", true);
            result.put("gameType", gameType);
            result.put("progress", progress);
            result.put("message", "继续游戏");

            return result;
        } catch (Exception e) {
            logger.error("Error continuing game", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "继续游戏失败");
            return result;
        }
    }

    @Override
    @Transactional
    public boolean pauseGame(Long userId, String gameCode, String gameBoard, Long currentScore, Integer playTime) {
        try {
            GameProgress progress = getGameProgress(userId, gameCode);
            if (progress == null) {
                // 创建新的游戏进度
                progress = new GameProgress(userId, gameCode, gameBoard, 1);
            }

            progress.setCurrentScore(currentScore);
            progress.setGameBoard(gameBoard);
            progress.setPlayTime(playTime);
            progress.setIsPaused(true);

            return saveGameProgress(progress);
        } catch (Exception e) {
            logger.error("Error pausing game", e);
            return false;
        }
    }

    @Override
    @Transactional
    public Map<String, Object> finishGame(Long userId, String gameCode, Long finalScore, String gameData, Integer playTime, Boolean isCompleted) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 保存游戏记录
            UserGameRecord record = new UserGameRecord(userId, gameCode, finalScore, 1);
            record.setGameData(gameData);
            record.setPlayTime(playTime);
            record.setIsCompleted(isCompleted);

            boolean recordSaved = saveGameRecord(record);

            // 更新用户统计
            boolean statsSaved = updateUserGameStats(userId, gameCode, finalScore, playTime);

            // 删除游戏进度
            deleteGameProgress(userId, gameCode);

            // 获取更新后的统计数据
            UserGameStats updatedStats = getUserGameStats(userId, gameCode);

            result.put("success", recordSaved && statsSaved);
            result.put("userStats", updatedStats);
            result.put("isNewRecord", isNewRecord(updatedStats, finalScore));
            result.put("message", recordSaved && statsSaved ? "游戏结果保存成功" : "保存失败");

            return result;
        } catch (Exception e) {
            logger.error("Error finishing game", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "保存游戏结果失败");
            return result;
        }
    }

    @Override
    public List<UserGameStats> getUserAllGameStats(Long userId) {
        try {
            return gameMapper.selectUserAllGameStats(userId);
        } catch (Exception e) {
            logger.error("Error getting user all game stats", e);
            return List.of();
        }
    }

    /**
     * 判断是否是新记录
     */
    private boolean isNewRecord(UserGameStats stats, Long score) {
        return score.equals(stats.getBestScore());
    }
}
