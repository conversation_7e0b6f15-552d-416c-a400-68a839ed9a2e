package com.tuqiwei.service.impl;

import com.tuqiwei.entity.WheelConfig;
import com.tuqiwei.entity.WheelRecord;
import com.tuqiwei.mapper.WheelMapper;
import com.tuqiwei.service.WheelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 转盘服务实现
 */
@Service
public class WheelServiceImpl implements WheelService {
    
    private static final Logger logger = LoggerFactory.getLogger(WheelServiceImpl.class);
    private static final int MAX_RECORDS = 20; // 最大记录数
    
    @Autowired
    private WheelMapper wheelMapper;
    
    @Override
    public List<WheelConfig> getUserWheelConfigs(Long userId, String wheelType) {
        try {
            List<WheelConfig> configs = wheelMapper.selectUserWheelConfigs(userId, wheelType);
            
            // 如果用户没有配置，则初始化默认配置
            if (configs.isEmpty()) {
                initUserWheelConfigs(userId, wheelType);
                configs = wheelMapper.selectUserWheelConfigs(userId, wheelType);
            }
            
            return configs;
        } catch (Exception e) {
            logger.error("获取用户转盘配置失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    @Transactional
    public boolean saveUserWheelConfigs(Long userId, String wheelType, List<WheelConfig> configs) {
        try {
            // 删除原有配置
            wheelMapper.deleteUserWheelConfigs(userId, wheelType);
            
            // 插入新配置
            if (!configs.isEmpty()) {
                // 设置用户ID和转盘类型
                for (int i = 0; i < configs.size(); i++) {
                    WheelConfig config = configs.get(i);
                    config.setUserId(userId);
                    config.setWheelType(wheelType);
                    config.setSlotIndex(i);
                    config.setIsActive(true);
                    
                    // 验证权重范围
                    if (config.getWeight() == null || config.getWeight() < 1 || config.getWeight() > 100) {
                        config.setWeight(100);
                    }
                }
                
                wheelMapper.batchInsertWheelConfigs(configs);
            }
            
            return true;
        } catch (Exception e) {
            logger.error("保存用户转盘配置失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean initUserWheelConfigs(Long userId, String wheelType) {
        try {
            // 先删除用户现有的转盘配置
            wheelMapper.deleteUserWheelConfigs(userId, wheelType);

            // 获取默认配置
            List<WheelConfig> defaultConfigs = wheelMapper.selectDefaultWheelConfigs(wheelType);

            if (!defaultConfigs.isEmpty()) {
                // 复制默认配置给用户
                List<WheelConfig> userConfigs = new ArrayList<>();
                for (WheelConfig defaultConfig : defaultConfigs) {
                    WheelConfig userConfig = new WheelConfig(
                        userId,
                        wheelType,
                        defaultConfig.getSlotIndex(),
                        defaultConfig.getContent(),
                        defaultConfig.getWeight()
                    );
                    userConfigs.add(userConfig);
                }

                wheelMapper.batchInsertWheelConfigs(userConfigs);
            }

            return true;
        } catch (Exception e) {
            logger.error("初始化用户转盘配置失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public Map<String, Object> spinWheel(Long userId, String wheelType) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取用户转盘配置
            List<WheelConfig> configs = getUserWheelConfigs(userId, wheelType);
            
            if (configs.isEmpty()) {
                result.put("success", false);
                result.put("message", "转盘配置为空");
                return result;
            }
            
            // 根据权重随机选择
            String selectedContent = selectByWeight(configs);
            
            // 保存转盘记录
            WheelRecord record = new WheelRecord(userId, wheelType, selectedContent);
            wheelMapper.insertWheelRecord(record);
            
            // 维护记录数量限制
            maintainRecordLimit(userId);
            
            result.put("success", true);
            result.put("result", selectedContent);
            result.put("wheelType", wheelType);
            result.put("wheelTypeName", getWheelTypeName(wheelType));
            
            return result;
        } catch (Exception e) {
            logger.error("转盘旋转失败", e);
            result.put("success", false);
            result.put("message", "转盘旋转失败");
            return result;
        }
    }

    @Override
    public Map<String, Object> getUserWheelRecords(Long userId, int page, int size) {
        Map<String, Object> result = new HashMap<>();

        try {
            int offset = (page - 1) * size;
            List<WheelRecord> records = wheelMapper.selectUserWheelRecords(userId, offset, size);
            int total = wheelMapper.countUserWheelRecords(userId);

            result.put("records", records);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));

            return result;
        } catch (Exception e) {
            logger.error("获取用户转盘记录失败", e);
            result.put("records", new ArrayList<>());
            result.put("total", 0);
            return result;
        }
    }

    @Override
    @Transactional
    public boolean clearUserWheelRecords(Long userId) {
        try {
            wheelMapper.clearUserWheelRecords(userId);
            return true;
        } catch (Exception e) {
            logger.error("清空用户转盘记录失败", e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getWheelTypes() {
        List<Map<String, Object>> types = new ArrayList<>();

        Map<String, Object> eat = new HashMap<>();
        eat.put("type", "eat");
        eat.put("name", "吃什么");
        eat.put("icon", "🍽️");
        eat.put("description", "今天吃什么呢？让转盘来决定吧！");
        types.add(eat);

        Map<String, Object> play = new HashMap<>();
        play.put("type", "play");
        play.put("name", "玩什么");
        play.put("icon", "🎮");
        play.put("description", "休闲时光，来点什么娱乐活动？");
        types.add(play);

        Map<String, Object> doSomething = new HashMap<>();
        doSomething.put("type", "do");
        doSomething.put("name", "干什么");
        doSomething.put("icon", "📝");
        doSomething.put("description", "接下来该做什么事情呢？");
        types.add(doSomething);

        return types;
    }

    @Override
    public void cleanExpiredRecords() {
        try {
            int deletedCount = wheelMapper.deleteExpiredRecords();
            logger.info("清理过期转盘记录: {} 条", deletedCount);
        } catch (Exception e) {
            logger.error("清理过期记录失败", e);
        }
    }

    /**
     * 根据权重随机选择
     */
    private String selectByWeight(List<WheelConfig> configs) {
        // 计算总权重
        int totalWeight = configs.stream().mapToInt(WheelConfig::getWeight).sum();

        // 生成随机数
        Random random = new Random();
        int randomWeight = random.nextInt(totalWeight) + 1;

        // 根据权重选择
        int currentWeight = 0;
        for (WheelConfig config : configs) {
            currentWeight += config.getWeight();
            if (randomWeight <= currentWeight) {
                return config.getContent();
            }
        }

        // 默认返回第一个
        return configs.get(0).getContent();
    }

    /**
     * 维护记录数量限制
     */
    private void maintainRecordLimit(Long userId) {
        try {
            int totalRecords = wheelMapper.countUserWheelRecords(userId);
            if (totalRecords > MAX_RECORDS) {
                int deleteCount = totalRecords - MAX_RECORDS;
                wheelMapper.deleteOldestRecords(userId, deleteCount);
            }
        } catch (Exception e) {
            logger.error("维护记录数量限制失败", e);
        }
    }

    /**
     * 获取转盘类型中文名称
     */
    private String getWheelTypeName(String wheelType) {
        switch (wheelType) {
            case "eat": return "吃什么";
            case "play": return "玩什么";
            case "do": return "干什么";
            default: return wheelType;
        }
    }
}
