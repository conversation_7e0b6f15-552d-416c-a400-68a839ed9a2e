package com.tuqiwei.service.impl;

import com.tuqiwei.entity.User;
import com.tuqiwei.mapper.UserMapper;
import com.tuqiwei.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public User login(String username, String password) {
        User user = userMapper.selectByUsername(username);
        if (user != null && password.equals(user.getPassword())) {
            // 不返回密码信息
            user.setPassword(null);
            return user;
        }
        return null;
    }

    @Override
    public User getUserById(Long id) {
        User user = userMapper.selectById(id);
        if (user != null) {
            // 不返回密码信息
            user.setPassword(null);
        }
        return user;
    }

    @Override
    public boolean updateUser(User user) {
        try {
            int result = userMapper.updateUser(user);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return false;
        }
    }

    @Override
    public boolean updatePassword(Long id, String oldPassword, String newPassword) {
        try {
            // 先验证旧密码
            User user = userMapper.selectById(id);
            if (user == null || !oldPassword.equals(user.getPassword())) {
                return false;
            }
            
            int result = userMapper.updatePassword(id, newPassword);
            return result > 0;
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return false;
        }
    }

    @Override
    public boolean updateAvatar(Long id, String avatar) {
        try {
            int result = userMapper.updateAvatar(id, avatar);
            return result > 0;
        } catch (Exception e) {
            log.error("更新头像失败", e);
            return false;
        }
    }
}
