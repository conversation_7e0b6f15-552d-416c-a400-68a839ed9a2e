package com.tuqiwei.service.impl;

import com.tuqiwei.entity.AdminUser;
import com.tuqiwei.entity.User;
import com.tuqiwei.mapper.AdminMapper;
import com.tuqiwei.service.AdminService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 管理员服务实现
 */
@Service
public class AdminServiceImpl implements AdminService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminServiceImpl.class);
    
    @Autowired
    private AdminMapper adminMapper;
    
    @Override
    public Map<String, Object> login(String username, String password) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询管理员
            AdminUser admin = adminMapper.selectByUsername(username);
            
            if (admin == null) {
                result.put("success", false);
                result.put("message", "管理员账号不存在");
                return result;
            }
            
            if (!admin.getStatus()) {
                result.put("success", false);
                result.put("message", "账号已被禁用");
                return result;
            }
            
            // 验证密码（这里简单比较，实际项目中应该使用加密）
            if (!password.equals(admin.getPassword())) {
                result.put("success", false);
                result.put("message", "密码错误");
                return result;
            }
            
            // 更新最后登录时间
            adminMapper.updateLastLoginTime(admin.getId());
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("adminInfo", admin);
            
            return result;
        } catch (Exception e) {
            logger.error("管理员登录失败", e);
            result.put("success", false);
            result.put("message", "登录失败");
            return result;
        }
    }
    
    @Override
    public AdminUser getAdminInfo(Long adminId) {
        try {
            return adminMapper.selectByUsername("admin"); // 简化处理，实际应该根据ID查询
        } catch (Exception e) {
            logger.error("获取管理员信息失败", e);
            return null;
        }
    }
    
    @Override
    public Map<String, Object> getUserList(String keyword, Boolean status, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int offset = (page - 1) * size;
            List<User> users = adminMapper.selectUserList(keyword, status, offset, size);
            int total = adminMapper.countUsers(keyword, status);
            
            result.put("users", users);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return result;
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            result.put("users", new ArrayList<>());
            result.put("total", 0);
            return result;
        }
    }
    
    @Override
    public boolean updateUserStatus(Long userId, Boolean status) {
        try {
            int rows = adminMapper.updateUserStatus(userId, status);
            return rows > 0;
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取用户统计
            Map<String, Object> userStats = adminMapper.getUserStats();
            result.put("userStats", userStats);
            
            // 获取聊天统计
            Map<String, Object> chatStats = adminMapper.getChatStats();
            result.put("chatStats", chatStats);
            
            // 获取游戏统计
            Map<String, Object> gameStats = adminMapper.getGameStats();
            result.put("gameStats", gameStats);
            
            return result;
        } catch (Exception e) {
            logger.error("获取统计数据失败", e);
            return new HashMap<>();
        }
    }
    
    @Override
    public Map<String, Object> getUserTrendData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取用户注册趋势
            List<Map<String, Object>> userTrend = adminMapper.getUserTrend();
            result.put("userTrend", userTrend);
            
            // 获取活跃用户趋势
            List<Map<String, Object>> activeUserTrend = adminMapper.getActiveUserTrend();
            result.put("activeUserTrend", activeUserTrend);
            
            return result;
        } catch (Exception e) {
            logger.error("获取趋势数据失败", e);
            return new HashMap<>();
        }
    }
}
