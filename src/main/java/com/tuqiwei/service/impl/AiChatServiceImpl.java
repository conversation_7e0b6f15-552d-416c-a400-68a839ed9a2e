package com.tuqiwei.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tuqiwei.config.AiConfig;
import com.tuqiwei.entity.ChatMessage;
import com.tuqiwei.entity.ChatSession;
import com.tuqiwei.mapper.ChatMessageMapper;
import com.tuqiwei.mapper.ChatSessionMapper;
import com.tuqiwei.service.AiChatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI聊天服务实现类
 */
@Service
public class AiChatServiceImpl implements AiChatService {
    
    private static final Logger logger = LoggerFactory.getLogger(AiChatServiceImpl.class);
    
    @Autowired
    private WebClient aiWebClient;
    
    @Autowired
    private AiConfig aiConfig;
    
    @Autowired
    private ChatSessionMapper chatSessionMapper;
    
    @Autowired
    private ChatMessageMapper chatMessageMapper;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public Flux<String> sendMessage(Long sessionId, String userMessage, Long userId) {
        try {
            // 保存用户消息
            ChatMessage userMsg = new ChatMessage(sessionId, userId, "user", userMessage);
            chatMessageMapper.insert(userMsg);
            
            // 获取会话历史
            List<ChatMessage> history = chatMessageMapper.selectBySessionId(sessionId);
            
            // 构建请求体
            Map<String, Object> requestBody = buildChatRequest(history);
            
            // 发送请求并处理流式响应
            return aiWebClient.post()
                    .uri("/v1/chat/completions")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToFlux(String.class)
                    .map(this::parseStreamResponse)
                    .filter(content -> content != null && !content.isEmpty())
                    .doOnComplete(() -> {
                        // 流式响应完成后，保存完整的AI回复
                        // 这里需要收集所有的流式内容
                        logger.info("Stream response completed for session: {}", sessionId);
                    })
                    .doOnError(error -> {
                        logger.error("Error in stream response for session: {}", sessionId, error);
                    });
                    
        } catch (Exception e) {
            logger.error("Error sending message to AI", e);
            return Flux.error(e);
        }
    }
    
    @Override
    public String sendMessageSync(Long sessionId, String userMessage, Long userId) {
        try {
            // 保存用户消息
            ChatMessage userMsg = new ChatMessage(sessionId, userId, "user", userMessage);
            chatMessageMapper.insert(userMsg);

            // 获取会话历史
            List<ChatMessage> history = chatMessageMapper.selectBySessionId(sessionId);

            // 构建请求体（非流式）
            Map<String, Object> requestBody = buildChatRequest(history);
            requestBody.put("stream", false);

            // 发送请求到AI API
            String response = aiWebClient.post()
                    .uri("/v1/chat/completions")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            // 解析响应
            String aiReply = parseNonStreamResponse(response);

            // 如果AI API调用失败，使用备用回复
            if (aiReply == null || aiReply.isEmpty()) {
                aiReply = generateMockResponse(userMessage);
            }

            // 保存AI回复
            if (aiReply != null && !aiReply.isEmpty()) {
                ChatMessage aiMsg = new ChatMessage(sessionId, userId, "assistant", aiReply);
                chatMessageMapper.insert(aiMsg);
            }

            return aiReply;

        } catch (Exception e) {
            logger.error("Error sending sync message to AI", e);
            return "抱歉，AI服务暂时不可用，请稍后再试。";
        }
    }

    /**
     * 生成模拟回复（临时用于测试）
     */
    private String generateMockResponse(String userMessage) {
        if (userMessage.contains("你好") || userMessage.contains("hello")) {
            return "你好！很高兴见到你！我是你的AI助手，有什么可以帮助你的吗？";
        } else if (userMessage.contains("天气")) {
            return "抱歉，我目前无法获取实时天气信息。建议你查看天气预报应用获取准确的天气信息。";
        } else if (userMessage.contains("时间")) {
            return "当前时间是：" + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            return "我收到了你的消息：\"" + userMessage + "\"。这是一个测试回复，AI功能正在完善中。有什么其他问题吗？";
        }
    }
    
    @Override
    public ChatSession createSession(Long userId, String sessionName, String sessionType) {
        ChatSession session = new ChatSession(userId, sessionName, sessionType);
        chatSessionMapper.insert(session);
        return session;
    }
    
    @Override
    public List<ChatSession> getUserSessions(Long userId) {
        return chatSessionMapper.selectByUserId(userId);
    }
    
    @Override
    public List<ChatMessage> getSessionMessages(Long sessionId) {
        return chatMessageMapper.selectBySessionId(sessionId);
    }
    
    @Override
    public boolean deleteSession(Long sessionId, Long userId) {
        // 验证会话属于该用户
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session == null || !session.getUserId().equals(userId)) {
            return false;
        }
        
        // 软删除会话
        return chatSessionMapper.deleteById(sessionId) > 0;
    }
    
    @Override
    public boolean renameSession(Long sessionId, String newName, Long userId) {
        // 验证会话属于该用户
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session == null || !session.getUserId().equals(userId)) {
            return false;
        }
        
        return chatSessionMapper.updateSessionName(sessionId, newName) > 0;
    }
    
    @Override
    public boolean toggleMessageFavorite(Long messageId, Long userId, Boolean isFavorite) {
        // 验证消息属于该用户
        ChatMessage message = chatMessageMapper.selectById(messageId);
        if (message == null || !message.getUserId().equals(userId)) {
            return false;
        }
        
        return chatMessageMapper.updateFavorite(messageId, isFavorite) > 0;
    }
    
    @Override
    public List<ChatMessage> getFavoriteMessages(Long userId) {
        return chatMessageMapper.selectFavoritesByUserId(userId);
    }
    
    /**
     * 构建聊天请求体
     */
    private Map<String, Object> buildChatRequest(List<ChatMessage> history) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", aiConfig.getChat().getOptions().getModel());
        requestBody.put("temperature", aiConfig.getChat().getOptions().getTemperature());
        requestBody.put("max_tokens", aiConfig.getChat().getOptions().getMaxTokens());
        requestBody.put("stream", aiConfig.getChat().getOptions().getStream());
        
        // 构建消息列表
        List<Map<String, String>> messages = new ArrayList<>();
        for (ChatMessage msg : history) {
            Map<String, String> message = new HashMap<>();
            message.put("role", msg.getRole());
            message.put("content", msg.getContent());
            messages.add(message);
        }
        requestBody.put("messages", messages);
        
        return requestBody;
    }
    
    /**
     * 解析流式响应
     */
    private String parseStreamResponse(String chunk) {
        try {
            if (chunk.startsWith("data: ")) {
                String jsonData = chunk.substring(6).trim();
                if ("[DONE]".equals(jsonData)) {
                    return null;
                }
                
                JsonNode jsonNode = objectMapper.readTree(jsonData);
                JsonNode choices = jsonNode.get("choices");
                if (choices != null && choices.isArray() && choices.size() > 0) {
                    JsonNode delta = choices.get(0).get("delta");
                    if (delta != null && delta.has("content")) {
                        return delta.get("content").asText();
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("Error parsing stream response chunk: {}", chunk, e);
        }
        return null;
    }
    
    /**
     * 解析非流式响应
     */
    private String parseNonStreamResponse(String response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode choices = jsonNode.get("choices");
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode message = choices.get(0).get("message");
                if (message != null && message.has("content")) {
                    return message.get("content").asText();
                }
            }
        } catch (Exception e) {
            logger.error("Error parsing non-stream response: {}", response, e);
        }
        return null;
    }
}
