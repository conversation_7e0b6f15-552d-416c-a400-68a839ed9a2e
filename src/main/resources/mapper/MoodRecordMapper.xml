<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuqiwei.mapper.MoodRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.tuqiwei.entity.MoodRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="session_id" property="sessionId" jdbcType="BIGINT"/>
        <result column="mood_type" property="moodType" jdbcType="VARCHAR"/>
        <result column="mood_level" property="moodLevel" jdbcType="INTEGER"/>
        <result column="mood_description" property="moodDescription" jdbcType="LONGVARCHAR"/>
        <result column="trigger_event" property="triggerEvent" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, session_id, mood_type, mood_level, mood_description, trigger_event, created_time
    </sql>

    <!-- 插入心情记录 -->
    <insert id="insert" parameterType="com.tuqiwei.entity.MoodRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO mood_records (user_id, session_id, mood_type, mood_level, mood_description, trigger_event)
        VALUES (#{userId}, #{sessionId}, #{moodType}, #{moodLevel}, #{moodDescription}, #{triggerEvent})
    </insert>

    <!-- 根据用户ID查询心情记录 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM mood_records
        WHERE user_id = #{userId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据会话ID查询心情记录 -->
    <select id="selectBySessionId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM mood_records
        WHERE session_id = #{sessionId}
        ORDER BY created_time DESC
    </select>

    <!-- 查询用户最近的心情记录 -->
    <select id="selectLatestByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM mood_records
        WHERE user_id = #{userId}
        ORDER BY created_time DESC
        LIMIT 1
    </select>

    <!-- 根据时间范围查询用户心情记录 -->
    <select id="selectByUserIdAndTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM mood_records
        WHERE user_id = #{userId}
        AND created_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY created_time DESC
    </select>

    <!-- 统计用户各种心情类型的次数 -->
    <select id="selectMoodStatsByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT mood_type, COUNT(*) as mood_level
        FROM mood_records
        WHERE user_id = #{userId}
        GROUP BY mood_type
        ORDER BY COUNT(*) DESC
    </select>

    <!-- 删除心情记录 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM mood_records WHERE id = #{id}
    </delete>

</mapper>
