<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuqiwei.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.tuqiwei.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, username, password, name, phone, email, address, avatar, create_time, update_time
    </sql>

    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE username = #{username}
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE id = #{id}
    </select>

    <update id="updateUser" parameterType="com.tuqiwei.entity.User">
        UPDATE user
        SET name = #{name},
            phone = #{phone},
            email = #{email},
            address = #{address},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="updatePassword">
        UPDATE user
        SET password = #{password},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="updateAvatar">
        UPDATE user
        SET avatar = #{avatar},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
