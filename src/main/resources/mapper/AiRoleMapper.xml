<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuqiwei.mapper.AiRoleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.tuqiwei.entity.AiRole">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_type" property="roleType" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="system_prompt" property="systemPrompt" jdbcType="LONGVARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="is_active" property="isActive" jdbcType="TINYINT"/>
        <result column="usage_count" property="usageCount" jdbcType="INTEGER"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, role_name, role_type, category, avatar_url, description, system_prompt, 
        user_id, is_active, usage_count, created_time, updated_time
    </sql>

    <!-- 插入角色 -->
    <insert id="insert" parameterType="com.tuqiwei.entity.AiRole" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_roles (role_name, role_type, category, avatar_url, description, 
                             system_prompt, user_id, is_active, usage_count)
        VALUES (#{roleName}, #{roleType}, #{category}, #{avatarUrl}, #{description}, 
                #{systemPrompt}, #{userId}, #{isActive}, #{usageCount})
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_roles
        WHERE id = #{id} AND is_active = 1
    </select>

    <!-- 查询所有系统角色 -->
    <select id="selectSystemRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_roles
        WHERE role_type = 'system' AND is_active = 1
        ORDER BY usage_count DESC, created_time ASC
    </select>

    <!-- 根据分类查询角色 -->
    <select id="selectByCategory" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_roles
        WHERE category = #{category} AND is_active = 1
        ORDER BY usage_count DESC, created_time ASC
    </select>

    <!-- 根据用户ID查询自定义角色 -->
    <select id="selectCustomRolesByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_roles
        WHERE user_id = #{userId} AND role_type = 'custom' AND is_active = 1
        ORDER BY created_time DESC
    </select>

    <!-- 查询热门角色 -->
    <select id="selectPopularRoles" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_roles
        WHERE is_active = 1
        ORDER BY usage_count DESC
        LIMIT #{limit}
    </select>

    <!-- 更新角色信息 -->
    <update id="updateById" parameterType="com.tuqiwei.entity.AiRole">
        UPDATE ai_roles
        SET role_name = #{roleName},
            category = #{category},
            avatar_url = #{avatarUrl},
            description = #{description},
            system_prompt = #{systemPrompt}
        WHERE id = #{id}
    </update>

    <!-- 增加使用次数 -->
    <update id="incrementUsageCount" parameterType="java.lang.Long">
        UPDATE ai_roles
        SET usage_count = usage_count + 1
        WHERE id = #{id}
    </update>

    <!-- 更新角色状态 -->
    <update id="updateStatus">
        UPDATE ai_roles
        SET is_active = #{isActive}
        WHERE id = #{id}
    </update>

    <!-- 删除角色 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_roles WHERE id = #{id}
    </delete>

    <!-- 统计角色数量 -->
    <select id="countByCategory" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ai_roles
        WHERE category = #{category} AND is_active = 1
    </select>

</mapper>
