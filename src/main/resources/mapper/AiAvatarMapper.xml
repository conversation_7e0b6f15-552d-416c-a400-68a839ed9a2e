<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuqiwei.mapper.AiAvatarMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.tuqiwei.entity.AiAvatar">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="avatar_name" property="avatarName" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="is_default" property="isDefault" jdbcType="TINYINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, avatar_name, avatar_url, is_default, created_time, updated_time
    </sql>

    <!-- 插入AI头像 -->
    <insert id="insert" parameterType="com.tuqiwei.entity.AiAvatar" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_avatars (user_id, avatar_name, avatar_url, is_default)
        VALUES (#{userId}, #{avatarName}, #{avatarUrl}, #{isDefault})
    </insert>

    <!-- 根据用户ID查询AI头像列表 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_avatars
        WHERE user_id = #{userId}
        ORDER BY is_default DESC, created_time ASC
    </select>

    <!-- 根据用户ID获取默认AI头像 -->
    <select id="selectDefaultByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_avatars
        WHERE user_id = #{userId} AND is_default = 1
        LIMIT 1
    </select>

    <!-- 设置默认头像 -->
    <update id="setDefaultAvatar">
        UPDATE ai_avatars
        SET is_default = 1
        WHERE id = #{avatarId} AND user_id = #{userId}
    </update>

    <!-- 取消所有默认头像 -->
    <update id="clearDefaultAvatars" parameterType="java.lang.Long">
        UPDATE ai_avatars
        SET is_default = 0
        WHERE user_id = #{userId}
    </update>

    <!-- 删除头像 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_avatars WHERE id = #{id}
    </delete>

    <!-- 更新头像信息 -->
    <update id="updateById" parameterType="com.tuqiwei.entity.AiAvatar">
        UPDATE ai_avatars
        SET avatar_name = #{avatarName},
            avatar_url = #{avatarUrl}
        WHERE id = #{id}
    </update>

</mapper>
