<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuqiwei.mapper.ComfortScenarioMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.tuqiwei.entity.ComfortScenario">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="scenario_name" property="scenarioName" jdbcType="VARCHAR"/>
        <result column="scenario_type" property="scenarioType" jdbcType="VARCHAR"/>
        <result column="mood_types" property="moodTypes" jdbcType="VARCHAR"/>
        <result column="system_prompt" property="systemPrompt" jdbcType="LONGVARCHAR"/>
        <result column="greeting_message" property="greetingMessage" jdbcType="LONGVARCHAR"/>
        <result column="comfort_tips" property="comfortTips" jdbcType="LONGVARCHAR"/>
        <result column="is_active" property="isActive" jdbcType="TINYINT"/>
        <result column="usage_count" property="usageCount" jdbcType="INTEGER"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, scenario_name, scenario_type, mood_types, system_prompt, greeting_message, 
        comfort_tips, is_active, usage_count, created_time, updated_time
    </sql>

    <!-- 查询所有启用的场景 -->
    <select id="selectActiveScenarios" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM comfort_scenarios
        WHERE is_active = 1
        ORDER BY usage_count DESC, created_time ASC
    </select>

    <!-- 根据场景类型查询 -->
    <select id="selectByScenarioType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM comfort_scenarios
        WHERE scenario_type = #{scenarioType} AND is_active = 1
        ORDER BY usage_count DESC
    </select>

    <!-- 根据心情类型查询适合的场景 -->
    <select id="selectByMoodType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM comfort_scenarios
        WHERE FIND_IN_SET(#{moodType}, mood_types) > 0 AND is_active = 1
        ORDER BY usage_count DESC
    </select>

    <!-- 根据ID查询场景 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM comfort_scenarios
        WHERE id = #{id}
    </select>

    <!-- 增加使用次数 -->
    <update id="incrementUsageCount" parameterType="java.lang.Long">
        UPDATE comfort_scenarios
        SET usage_count = usage_count + 1,
            updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 插入新场景 -->
    <insert id="insert" parameterType="com.tuqiwei.entity.ComfortScenario" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO comfort_scenarios (scenario_name, scenario_type, mood_types, system_prompt, 
                                     greeting_message, comfort_tips, is_active, usage_count)
        VALUES (#{scenarioName}, #{scenarioType}, #{moodTypes}, #{systemPrompt}, 
                #{greetingMessage}, #{comfortTips}, #{isActive}, #{usageCount})
    </insert>

    <!-- 更新场景 -->
    <update id="updateById" parameterType="com.tuqiwei.entity.ComfortScenario">
        UPDATE comfort_scenarios
        SET scenario_name = #{scenarioName},
            scenario_type = #{scenarioType},
            mood_types = #{moodTypes},
            system_prompt = #{systemPrompt},
            greeting_message = #{greetingMessage},
            comfort_tips = #{comfortTips},
            is_active = #{isActive},
            updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除场景 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM comfort_scenarios WHERE id = #{id}
    </delete>

</mapper>
