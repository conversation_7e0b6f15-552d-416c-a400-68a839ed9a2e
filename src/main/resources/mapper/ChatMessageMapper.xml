<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuqiwei.mapper.ChatMessageMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.tuqiwei.entity.ChatMessage">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="session_id" property="sessionId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="role" property="role" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="message_type" property="messageType" jdbcType="VARCHAR"/>
        <result column="metadata" property="metadata" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="is_favorite" property="isFavorite" jdbcType="TINYINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, session_id, user_id, role, content, message_type, metadata, status, is_favorite, created_time
    </sql>

    <!-- 插入消息 -->
    <insert id="insert" parameterType="com.tuqiwei.entity.ChatMessage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_messages (session_id, user_id, role, content, message_type, metadata, status, is_favorite)
        VALUES (#{sessionId}, #{userId}, #{role}, #{content}, #{messageType}, #{metadata}, #{status}, #{isFavorite})
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_messages
        WHERE id = #{id}
    </select>

    <!-- 根据会话ID查询消息列表 -->
    <select id="selectBySessionId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_messages
        WHERE session_id = #{sessionId}
        ORDER BY created_time ASC
    </select>

    <!-- 根据会话ID分页查询消息列表 -->
    <select id="selectBySessionIdWithPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_messages
        WHERE session_id = #{sessionId}
        ORDER BY created_time ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据用户ID查询收藏的消息 -->
    <select id="selectFavoritesByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_messages
        WHERE user_id = #{userId} AND is_favorite = 1
        ORDER BY created_time DESC
    </select>

    <!-- 更新消息状态 -->
    <update id="updateStatus">
        UPDATE chat_messages
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 更新消息收藏状态 -->
    <update id="updateFavorite">
        UPDATE chat_messages
        SET is_favorite = #{isFavorite}
        WHERE id = #{id}
    </update>

    <!-- 删除消息 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM chat_messages WHERE id = #{id}
    </delete>

    <!-- 根据会话ID删除所有消息 -->
    <delete id="deleteBySessionId" parameterType="java.lang.Long">
        DELETE FROM chat_messages WHERE session_id = #{sessionId}
    </delete>

    <!-- 统计会话消息数量 -->
    <select id="countBySessionId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_messages
        WHERE session_id = #{sessionId}
    </select>

    <!-- 统计用户今日消息数量 -->
    <select id="countTodayByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_messages
        WHERE user_id = #{userId}
        AND DATE(created_time) = CURDATE()
    </select>

    <!-- 获取会话的最后一条消息 -->
    <select id="selectLastMessageBySessionId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_messages
        WHERE session_id = #{sessionId}
        ORDER BY created_time DESC
        LIMIT 1
    </select>

</mapper>
