<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuqiwei.mapper.ChatSessionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.tuqiwei.entity.ChatSession">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="session_name" property="sessionName" jdbcType="VARCHAR"/>
        <result column="session_type" property="sessionType" jdbcType="VARCHAR"/>
        <result column="config" property="config" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, session_name, session_type, config, created_time, updated_time, is_deleted
    </sql>

    <!-- 插入会话 -->
    <insert id="insert" parameterType="com.tuqiwei.entity.ChatSession" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_sessions (user_id, session_name, session_type, config, is_deleted)
        VALUES (#{userId}, #{sessionName}, #{sessionType}, #{config}, #{isDeleted})
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_sessions
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <!-- 根据用户ID查询会话列表 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_sessions
        WHERE user_id = #{userId} AND is_deleted = 0
        ORDER BY updated_time DESC
    </select>

    <!-- 根据用户ID和会话类型查询 -->
    <select id="selectByUserIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_sessions
        WHERE user_id = #{userId} AND session_type = #{sessionType} AND is_deleted = 0
        ORDER BY updated_time DESC
    </select>

    <!-- 更新会话信息 -->
    <update id="updateById" parameterType="com.tuqiwei.entity.ChatSession">
        UPDATE chat_sessions
        SET session_name = #{sessionName},
            session_type = #{sessionType},
            config = #{config}
        WHERE id = #{id}
    </update>

    <!-- 更新会话名称 -->
    <update id="updateSessionName">
        UPDATE chat_sessions
        SET session_name = #{sessionName}
        WHERE id = #{id}
    </update>

    <!-- 软删除会话 -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE chat_sessions
        SET is_deleted = 1
        WHERE id = #{id}
    </update>

    <!-- 物理删除会话 -->
    <delete id="physicalDeleteById" parameterType="java.lang.Long">
        DELETE FROM chat_sessions WHERE id = #{id}
    </delete>

    <!-- 统计用户会话数量 -->
    <select id="countByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_sessions
        WHERE user_id = #{userId} AND is_deleted = 0
    </select>

    <!-- 统计用户今日会话数量 -->
    <select id="countTodayByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_sessions
        WHERE user_id = #{userId} AND is_deleted = 0
        AND DATE(created_time) = CURDATE()
    </select>

</mapper>
