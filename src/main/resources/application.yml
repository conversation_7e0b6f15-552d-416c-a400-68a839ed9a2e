server:
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************
    username: root
    password: 200363
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.tuqiwei.entity
  configuration:
    map-underscore-to-camel-case: true

# 阿里云OSS配置
alioss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  access-key-id: LTAI5t5rQoWPq1Z6YAG6szWE
  access-key-secret: ******************************
  bucket-name: cqwm-tqw

# AI配置
ai:
  openai:
    base-url: https://api.deepseek.com
    api-key: ***********************************
    chat:
      options:
        model: deepseek-chat

logging:
  level:
    com.tuqiwei: debug
