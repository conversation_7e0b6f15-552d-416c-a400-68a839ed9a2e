-- AI聊天相关数据表

-- 1. 聊天会话表
CREATE TABLE IF NOT EXISTS chat_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_name VARCHAR(100) NOT NULL DEFAULT '新对话' COMMENT '会话名称',
    session_type VARCHAR(20) NOT NULL DEFAULT 'chat' COMMENT '会话类型：chat-基础聊天，comfort-哄哄模拟器，roleplay-角色扮演',
    config JSON COMMENT '会话配置（角色设定、场景等）',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    INDEX idx_user_id (user_id),
    INDEX idx_session_type (session_type),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 2. 聊天消息表
CREATE TABLE IF NOT EXISTS chat_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) NOT NULL COMMENT '角色：user-用户，assistant-AI助手，system-系统',
    content TEXT NOT NULL COMMENT '消息内容',
    message_type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '消息类型：text-文本，image-图片，file-文件',
    metadata JSON COMMENT '消息元数据（图片URL、文件信息等）',
    status VARCHAR(20) NOT NULL DEFAULT 'sent' COMMENT '消息状态：sending-发送中，sent-已发送，failed-失败',
    is_favorite TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否收藏：0-否，1-是',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_time (created_time),
    INDEX idx_is_favorite (is_favorite),
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 3. 用户AI使用统计表
CREATE TABLE IF NOT EXISTS user_ai_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    chat_count INT NOT NULL DEFAULT 0 COMMENT '聊天次数',
    message_count INT NOT NULL DEFAULT 0 COMMENT '消息数量',
    session_count INT NOT NULL DEFAULT 0 COMMENT '会话数量',
    total_duration INT NOT NULL DEFAULT 0 COMMENT '总使用时长（秒）',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_date (user_id, stat_date),
    INDEX idx_stat_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户AI使用统计表';

-- 4. 快捷回复表
CREATE TABLE IF NOT EXISTS quick_replies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    title VARCHAR(50) NOT NULL COMMENT '快捷回复标题',
    content TEXT NOT NULL COMMENT '快捷回复内容',
    category VARCHAR(20) NOT NULL DEFAULT 'common' COMMENT '分类：common-通用，comfort-哄人，roleplay-角色扮演',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_category (category),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷回复表';

-- 5. AI角色配置表
CREATE TABLE IF NOT EXISTS ai_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_type VARCHAR(20) NOT NULL COMMENT '角色类型：system-系统预设，custom-用户自定义',
    category VARCHAR(20) NOT NULL COMMENT '角色分类：poet-诗人，scientist-科学家，anime-动漫，etc',
    avatar_url VARCHAR(255) COMMENT '角色头像URL',
    description TEXT COMMENT '角色描述',
    system_prompt TEXT NOT NULL COMMENT '系统提示词',
    user_id BIGINT COMMENT '创建用户ID（自定义角色）',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0-否，1-是',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_role_type (role_type),
    INDEX idx_category (category),
    INDEX idx_user_id (user_id),
    INDEX idx_usage_count (usage_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色配置表';

-- 插入一些默认的AI角色
INSERT INTO ai_roles (role_name, role_type, category, description, system_prompt) VALUES
('智能助手', 'system', 'assistant', '通用智能助手，可以回答各种问题', '你是一个智能助手，请用友好、专业的语气回答用户的问题。'),
('李白', 'system', 'poet', '唐代著名诗人，豪放派代表', '你是唐代诗人李白，请用古典诗意的语言与用户对话，可以即兴作诗。'),
('爱因斯坦', 'system', 'scientist', '著名物理学家，相对论创立者', '你是物理学家爱因斯坦，请用科学的思维和幽默的语言与用户交流。'),
('心理咨询师', 'system', 'counselor', '专业心理咨询师，善于倾听和引导', '你是一位专业的心理咨询师，请用温和、理解的语气帮助用户解决心理问题。');

-- 插入一些默认的快捷回复
INSERT INTO quick_replies (user_id, title, content, category, sort_order) VALUES
(1, '问候', '你好！有什么可以帮助你的吗？', 'common', 1),
(1, '感谢', '谢谢你的提问，希望我的回答对你有帮助！', 'common', 2),
(1, '道歉', '对不起，我刚才的话可能让你不开心了', 'comfort', 1),
(1, '安慰', '我理解你现在的感受，我们一起想办法解决好吗？', 'comfort', 2),
(1, '角色开始', '好的，让我们开始角色扮演吧！', 'roleplay', 1);
