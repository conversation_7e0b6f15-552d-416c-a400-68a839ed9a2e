-- 小游戏相关数据表

USE tuqiwei;

-- 1. 游戏类型表
CREATE TABLE IF NOT EXISTS game_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    game_name VARCHAR(50) NOT NULL COMMENT '游戏名称',
    game_code VARCHAR(20) NOT NULL UNIQUE COMMENT '游戏代码',
    game_icon VARCHAR(200) COMMENT '游戏图标URL',
    game_description TEXT COMMENT '游戏描述',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_game_code (game_code),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏类型表';

-- 2. 用户游戏记录表
CREATE TABLE IF NOT EXISTS user_game_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    game_code VARCHAR(20) NOT NULL COMMENT '游戏代码',
    score BIGINT NOT NULL DEFAULT 0 COMMENT '游戏分数',
    level INT NOT NULL DEFAULT 1 COMMENT '游戏等级/难度',
    game_data JSON COMMENT '游戏数据（棋盘状态等）',
    play_time INT NOT NULL DEFAULT 0 COMMENT '游戏时长（秒）',
    is_completed TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否完成游戏',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_game (user_id, game_code),
    INDEX idx_score (score DESC),
    INDEX idx_created_time (created_time DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户游戏记录表';

-- 3. 用户游戏统计表
CREATE TABLE IF NOT EXISTS user_game_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    game_code VARCHAR(20) NOT NULL COMMENT '游戏代码',
    total_games INT NOT NULL DEFAULT 0 COMMENT '总游戏次数',
    best_score BIGINT NOT NULL DEFAULT 0 COMMENT '最高分',
    second_score BIGINT NOT NULL DEFAULT 0 COMMENT '第二高分',
    third_score BIGINT NOT NULL DEFAULT 0 COMMENT '第三高分',
    total_play_time INT NOT NULL DEFAULT 0 COMMENT '总游戏时长（秒）',
    last_play_time DATETIME COMMENT '最后游戏时间',
    game_settings JSON COMMENT '游戏设置（主题、难度等）',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_game (user_id, game_code),
    INDEX idx_best_score (best_score DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户游戏统计表';

-- 4. 游戏进度表（用于暂停/继续功能）
CREATE TABLE IF NOT EXISTS game_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    game_code VARCHAR(20) NOT NULL COMMENT '游戏代码',
    current_score BIGINT NOT NULL DEFAULT 0 COMMENT '当前分数',
    game_board JSON NOT NULL COMMENT '游戏棋盘状态',
    game_level INT NOT NULL DEFAULT 1 COMMENT '游戏难度',
    play_time INT NOT NULL DEFAULT 0 COMMENT '已游戏时长（秒）',
    is_paused TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否暂停',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_game_progress (user_id, game_code),
    INDEX idx_updated_time (updated_time DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏进度表';

-- 插入2048游戏类型
INSERT INTO game_types (game_name, game_code, game_description, sort_order) VALUES
('2048', '2048', '经典数字合成游戏，通过滑动方块合成更大的数字，目标是达到2048！', 1);

-- 验证数据
SELECT * FROM game_types;
